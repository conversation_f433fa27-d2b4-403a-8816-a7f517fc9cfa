package com.iotlaser.spms.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.AutoCodeRule;
import com.iotlaser.spms.base.domain.AutoCodePart;
import com.iotlaser.spms.base.domain.bo.AutoCodeRuleBo;
import com.iotlaser.spms.base.domain.vo.AutoCodeRuleVo;
import com.iotlaser.spms.base.mapper.AutoCodePartMapper;
import com.iotlaser.spms.base.mapper.AutoCodeRuleMapper;
import com.iotlaser.spms.base.service.IAutoCodeRuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 编码生成规则Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/03/11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AutoCodeRuleServiceImpl implements IAutoCodeRuleService {

    private final AutoCodeRuleMapper baseMapper;
    private final AutoCodePartMapper autoCodePartMapper;

    /**
     * 查询编码生成规则
     *
     * @param ruleId 主键
     * @return 编码生成规则
     */
    @Override
    public AutoCodeRuleVo queryById(Long ruleId) {
        return baseMapper.selectVoById(ruleId);
    }

    /**
     * 分页查询编码生成规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则分页列表
     */
    @Override
    public TableDataInfo<AutoCodeRuleVo> queryPageList(AutoCodeRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AutoCodeRule> lqw = buildQueryWrapper(bo);
        Page<AutoCodeRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的编码生成规则列表
     *
     * @param bo 查询条件
     * @return 编码生成规则列表
     */
    @Override
    public List<AutoCodeRuleVo> queryList(AutoCodeRuleBo bo) {
        LambdaQueryWrapper<AutoCodeRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AutoCodeRule> buildQueryWrapper(AutoCodeRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AutoCodeRule> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AutoCodeRule::getRuleId);
        lqw.eq(StringUtils.isNotBlank(bo.getRuleCode()), AutoCodeRule::getRuleCode, bo.getRuleCode());
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), AutoCodeRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleDesc()), AutoCodeRule::getRuleDesc, bo.getRuleDesc());
        lqw.eq(bo.getMaxLength() != null, AutoCodeRule::getMaxLength, bo.getMaxLength());
        lqw.eq(StringUtils.isNotBlank(bo.getIsPadded()), AutoCodeRule::getIsPadded, bo.getIsPadded());
        lqw.eq(StringUtils.isNotBlank(bo.getPaddedChar()), AutoCodeRule::getPaddedChar, bo.getPaddedChar());
        lqw.eq(StringUtils.isNotBlank(bo.getPaddedMethod()), AutoCodeRule::getPaddedMethod, bo.getPaddedMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), AutoCodeRule::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(AutoCodeRuleBo bo) {
        try {
            AutoCodeRule add = MapstructUtils.convert(bo, AutoCodeRule.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增编码生成规则失败");
            }

            bo.setRuleId(add.getRuleId());
            log.info("新增编码生成规则成功：规则编码【{}】规则名称【{}】", add.getRuleCode(), add.getRuleName());
            return true;
        } catch (Exception e) {
            log.error("新增编码生成规则失败：{}", e.getMessage(), e);
            throw new ServiceException("新增编码生成规则失败：" + e.getMessage());
        }
    }

    /**
     * 修改编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(AutoCodeRuleBo bo) {
        try {
            AutoCodeRule update = MapstructUtils.convert(bo, AutoCodeRule.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改编码生成规则失败：规则不存在或数据未变更");
            }

            log.info("修改编码生成规则成功：规则编码【{}】规则名称【{}】", update.getRuleCode(), update.getRuleName());
            return true;
        } catch (Exception e) {
            log.error("修改编码生成规则失败：{}", e.getMessage(), e);
            throw new ServiceException("修改编码生成规则失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AutoCodeRule entity) {
        // 校验规则编码唯一性
        if (StringUtils.isNotBlank(entity.getRuleCode())) {
            LambdaQueryWrapper<AutoCodeRule> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(AutoCodeRule::getRuleCode, entity.getRuleCode());
            if (entity.getRuleId() != null) {
                wrapper.ne(AutoCodeRule::getRuleId, entity.getRuleId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("编码规则编码已存在：" + entity.getRuleCode());
            }
        }
    }

    /**
     * 校验并批量删除编码生成规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验编码规则是否被使用
            // ✅ DDD重构：聚合根Service直接调用子实体Mapper，而不是通过子实体ServiceImpl
            List<AutoCodeRule> rules = baseMapper.selectByIds(ids);
            for (AutoCodeRule rule : rules) {
                // 直接使用Mapper查询子实体，符合DDD聚合根管理子实体的原则
                LambdaQueryWrapper<AutoCodePart> partWrapper = Wrappers.lambdaQuery();
                partWrapper.eq(AutoCodePart::getRuleId, rule.getRuleId());
                List<AutoCodePart> parts = autoCodePartMapper.selectList(partWrapper);
                if (!parts.isEmpty()) {
                    throw new ServiceException("编码规则【" + rule.getRuleName() + "】存在组成配置，不能删除");
                }
                log.info("删除编码规则校验：规则编码【{}】规则名称【{}】", rule.getRuleCode(), rule.getRuleName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除编码生成规则成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除编码生成规则失败：{}", e.getMessage(), e);
            throw new ServiceException("删除编码生成规则失败：" + e.getMessage());
        }
    }

    /**
     * 查询编码生成规则
     *
     * @param ruleCode 规则编码
     * @return 编码生成规则
     */
    @Override
    public AutoCodeRuleVo selectVoOneByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AutoCodeRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(AutoCodeRule::getRuleCode, ruleCode);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 启用编码规则
     *
     * @param ruleCode 规则编码
     * @return 是否启用成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableCodeRule(String ruleCode) {
        return updateCodeRuleStatus(ruleCode, "1");
    }

    /**
     * 禁用编码规则
     *
     * @param ruleCode 规则编码
     * @return 是否禁用成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableCodeRule(String ruleCode) {
        return updateCodeRuleStatus(ruleCode, "0");
    }

    /**
     * 更新编码规则状态
     *
     * @param ruleCode 规则编码
     * @param status   状态（1-启用，0-禁用）
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCodeRuleStatus(String ruleCode, String status) {
        try {
            // 检查规则是否存在
            AutoCodeRule rule = baseMapper.selectOne(
                Wrappers.lambdaQuery(AutoCodeRule.class)
                    .eq(AutoCodeRule::getRuleCode, ruleCode)
            );
            if (rule == null) {
                throw new ServiceException("编码规则不存在：" + ruleCode);
            }

            // 更新状态
            AutoCodeRule updateEntity = new AutoCodeRule();
            updateEntity.setRuleId(rule.getRuleId());
            updateEntity.setStatus(status);

            int result = baseMapper.updateById(updateEntity);
            if (result <= 0) {
                throw new ServiceException("更新编码规则状态失败");
            }

            String statusText = "1".equals(status) ? "启用" : "禁用";
            log.info("更新编码规则状态成功：{} - {}", rule.getRuleName(), statusText);
            return true;
        } catch (Exception e) {
            log.error("更新编码规则状态失败：{}", e.getMessage(), e);
            throw new ServiceException("更新编码规则状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有启用的编码规则
     *
     * @return 启用的编码规则列表
     */
    @Override
    public List<AutoCodeRuleVo> getActiveRules() {
        try {
            LambdaQueryWrapper<AutoCodeRule> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(AutoCodeRule::getStatus, "1");
            wrapper.orderByAsc(AutoCodeRule::getRuleCode);

            List<AutoCodeRuleVo> result = baseMapper.selectVoList(wrapper);
            log.debug("查询启用编码规则成功，数量：{}", result.size());
            return result;
        } catch (Exception e) {
            log.error("查询启用编码规则失败：{}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 重置编码序列号
     *
     * @param ruleCode    规则编码
     * @param startNumber 起始序号
     * @return 是否重置成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetCodeSequence(String ruleCode, Long startNumber) {
        try {
            // 检查规则是否存在
            AutoCodeRule rule = baseMapper.selectOne(
                Wrappers.lambdaQuery(AutoCodeRule.class)
                    .eq(AutoCodeRule::getRuleCode, ruleCode)
            );
            if (rule == null) {
                throw new ServiceException("编码规则不存在：" + ruleCode);
            }

            // TODO: 这里需要重置编码序列号，可能需要操作AutoCodeResult表
            // 暂时只记录日志
            log.info("重置编码序列号：规则【{}】起始序号【{}】", ruleCode, startNumber);

            // 可以考虑清空该规则的所有编码结果，或者更新下一个序号
            // autoCodeResultService.resetSequence(ruleCode, startNumber);

            return true;
        } catch (Exception e) {
            log.error("重置编码序列号失败：{}", e.getMessage(), e);
            throw new ServiceException("重置编码序列号失败：" + e.getMessage());
        }
    }
}
