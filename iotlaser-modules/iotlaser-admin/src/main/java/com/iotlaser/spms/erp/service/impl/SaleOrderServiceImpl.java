package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.erp.mapper.SaleOrderItemMapper;
import com.iotlaser.spms.erp.mapper.SaleOrderMapper;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import com.iotlaser.spms.erp.service.IFinancialReconciliationService;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import com.iotlaser.spms.erp.service.ISaleOutboundService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.UserService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_SALE_ORDER_CODE;

/**
 * 销售订单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SaleOrderServiceImpl implements ISaleOrderService {

    private final SaleOrderMapper baseMapper;
    private final SaleOrderItemMapper itemMapper;  // ✅ DDD正确：聚合根直接管理子实体Mapper

    // TODO: [DDD重构-跨聚合调用] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，聚合根Service不应该直接依赖其他聚合根Service
    // 当前保留这些依赖以维持业务功能完整性，后续需要重构为：
    // 1. 使用领域事件处理跨聚合的业务协调（如订单确认后创建出库单）
    // 2. 使用应用服务层协调多个聚合根的操作
    // 3. 将只读查询操作移到查询服务中
    private final ISaleOutboundService saleOutboundService;        // TEMP: 跨聚合调用
    private final ICompanyService companyService;                  // TEMP: 基础数据查询
    private final IProductService productService;                  // TEMP: 基础数据查询
    private final Gen gen;

    // 添加缺失的依赖注入 - 这些也是跨聚合调用，需要后续重构
    @Lazy
    @Autowired
    private IInventoryService inventoryService;                    // TEMP: 跨聚合调用
    @Lazy
    @Autowired
    private IFinArReceivableService finArReceivableService;        // TEMP: 跨聚合调用
    @Lazy
    @Autowired
    private IFinancialReconciliationService financialReconciliationService;  // TEMP: 跨聚合调用
    @Autowired
    private UserService userService;                               // TEMP: 基础服务调用

    /**
     * 查询销售订单
     *
     * @param orderId 主键
     * @return 销售订单
     */
    @Override
    public SaleOrderVo queryById(Long orderId) {
        return baseMapper.selectVoById(orderId);
    }

    /**
     * 分页查询销售订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单分页列表
     */
    @Override
    public TableDataInfo<SaleOrderVo> queryPageList(SaleOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SaleOrder> lqw = buildQueryWrapper(bo);
        Page<SaleOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的销售订单列表
     *
     * @param bo 查询条件
     * @return 销售订单列表
     */
    @Override
    public List<SaleOrderVo> queryList(SaleOrderBo bo) {
        LambdaQueryWrapper<SaleOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SaleOrder> buildQueryWrapper(SaleOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SaleOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SaleOrder::getOrderId);
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), SaleOrder::getOrderCode, bo.getOrderCode());
        lqw.eq(bo.getSourceId() != null, SaleOrder::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), SaleOrder::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(SaleOrder::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, SaleOrder::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), SaleOrder::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(SaleOrder::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getCustomerId() != null, SaleOrder::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), SaleOrder::getCustomerName, bo.getCustomerName());
        // 修复枚举查询：直接使用枚举类型
        if (bo.getOrderStatus() != null) {
            lqw.eq(SaleOrder::getOrderStatus, bo.getOrderStatus());
        }
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            SaleOrder::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * 新增销售订单
     *
     * @param bo 销售订单
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOrderVo insertByBo(SaleOrderBo bo) {
        try {
            // 生成订单编码
            if (StringUtils.isEmpty(bo.getOrderCode())) {
                bo.setOrderCode(gen.code(ERP_SALE_ORDER_CODE));
            }
            // 设置初始状态
            if (bo.getOrderStatus() == null) {
                bo.setOrderStatus(SaleOrderStatus.DRAFT);
            }
            // 设置下单日期
            if (bo.getOrderDate() == null) {
                bo.setOrderDate(LocalDate.now());
            }
            // 填充冗余信息
            fillRedundantFields(bo);
            // 转换为实体并校验
            SaleOrder add = MapstructUtils.convert(bo, SaleOrder.class);
            validEntityBeforeSave(add);
            // 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增销售订单失败");
            } else {
                if (add.getSourceType() == null || add.getSourceType() == SourceType.SALE_ORDER) {
                    add.setSourceId(add.getOrderId());
                    add.setSourceCode(add.getOrderCode());
                    add.setSourceType(SourceType.SALE_ORDER);
                }
                if (add.getDirectSourceType() == null || add.getDirectSourceType() == DirectSourceType.SALE_ORDER) {
                    add.setDirectSourceId(add.getOrderId());
                    add.setDirectSourceCode(add.getOrderCode());
                    add.setDirectSourceType(DirectSourceType.SALE_ORDER);
                }
                baseMapper.updateById(add);
            }

            log.info("新增销售订单成功：{}", add.getOrderCode());
            return MapstructUtils.convert(add, SaleOrderVo.class);
        } catch (Exception e) {
            log.error("新增销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("新增销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 修改销售订单
     *
     * @param bo 销售订单
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaleOrderVo updateByBo(SaleOrderBo bo) {
        try {
            // 填充冗余信息
            fillRedundantFields(bo);

            // 转换为实体并校验
            SaleOrder update = MapstructUtils.convert(bo, SaleOrder.class);
            validEntityBeforeSave(update);

            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改销售订单失败：订单不存在或数据未变更");
            }

            log.info("修改销售订单成功：{}", update.getOrderCode());
            return MapstructUtils.convert(update, SaleOrderVo.class);
        } catch (Exception e) {
            log.error("修改销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("修改销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SaleOrder entity) {
        // 校验订单编码唯一性
        if (StringUtils.isNotBlank(entity.getOrderCode())) {
            LambdaQueryWrapper<SaleOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(SaleOrder::getOrderCode, entity.getOrderCode());
            if (entity.getOrderId() != null) {
                wrapper.ne(SaleOrder::getOrderId, entity.getOrderId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("销售订单编码已存在：" + entity.getOrderCode());
            }
        }
    }

    /**
     * 校验并批量删除销售订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验销售订单是否可以删除
            List<SaleOrder> orders = baseMapper.selectByIds(ids);
            for (SaleOrder order : orders) {
                // 检查订单状态，只有草稿状态的订单才能删除
                if (SaleOrderStatus.DRAFT != order.getOrderStatus()) {
                    throw new ServiceException("销售订单【" + order.getOrderCode() + "】状态为【" +
                        order.getOrderStatus() + "】，不允许删除");
                }

                // 检查是否有关联的出库单
                if (saleOutboundService.existsByOrderId(order.getOrderId())) {
                    throw new ServiceException("销售订单【" + order.getOrderCode() + "】已有关联出库单，不允许删除");
                }


                log.info("删除销售订单校验：{}", order.getOrderCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除销售订单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 确认销售订单
     *
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态
            if (SaleOrderStatus.DRAFT != order.getOrderStatus()) {
                throw new ServiceException("只有草稿状态的订单才能确认");
            }

            // 校验订单明细
            List<SaleOrderItemVo> items = itemMapper.queryByOrderId(orderId);
            if (items.isEmpty()) {
                throw new ServiceException("订单明细不能为空");
            }

            for (SaleOrderItemVo item : items) {
                if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("订单明细【" + item.getProductName() + "】销售数量不能小于等于0");
                }
            }

            // 更新订单状态
            order.setOrderStatus(SaleOrderStatus.CONFIRMED);
            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("确认销售订单失败");
            }

            // TODO: [确认销售订单后的后续流程] - 优先级: MEDIUM - 参考文档: docs/design/README_FLOW.md
            // 确认成功后的可选操作（根据业务配置决定是否自动执行）：
            // 1. 自动创建销售出库单: saleOutboundService.createFromSaleOrder(order)
            // 2. 自动生成应收单: generateReceivableFromOrder(orderId)
            // 3. 发送确认通知给客户
            // 实现思路：通过配置参数控制是否自动执行这些操作

            log.info("确认销售订单成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("确认销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("确认销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 提交销售订单审批
     * ✅ 参考PurchaseOrderServiceImpl实现，集成warm-flow工作流引擎
     *
     * @param orderId 订单ID
     * @return 是否提交成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitForApproval(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态必须为草稿
            if (SaleOrderStatus.DRAFT != order.getOrderStatus()) {
                throw new ServiceException("只有草稿状态的订单才能提交审批");
            }

            // 校验订单明细完整性
            List<SaleOrderItemVo> items = itemMapper.queryByOrderId(orderId);
            if (items.isEmpty()) {
                throw new ServiceException("订单明细不能为空");
            }

            for (SaleOrderItemVo item : items) {
                if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("订单明细【" + item.getProductName() + "】销售数量不能小于等于0");
                }
                if (item.getPrice() == null || item.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("订单明细【" + item.getProductName() + "】销售价格不能小于等于0");
                }
            }

            // 校验客户信息完整性
            if (order.getCustomerId() == null) {
                throw new ServiceException("客户信息不能为空");
            }

            // 启动审批流程
            Map<String, Object> approvalVariables = prepareApprovalVariables(order);
            startApprovalProcess(order, approvalVariables);

            // 保持草稿状态，通过审批标记来区分是否在审批中
            // 注意：实际的状态流转由warm-flow工作流引擎控制
            // order.setOrderStatus(SaleOrderStatus.DRAFT); // 保持原状态
            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // 发送审批通知
                sendApprovalNotification(order);
                log.info("销售订单【{}】已成功提交审批", order.getOrderCode());
            }

            return result;
        } catch (Exception e) {
            log.error("提交销售订单审批失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("提交审批失败：" + e.getMessage());
        }
    }

    /**
     * 审批通过销售订单
     * ✅ 参考PurchaseOrderServiceImpl实现
     *
     * @param orderId 订单ID
     * @param approvalComment 审批意见
     * @return 是否审批成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean approveOrder(Long orderId, String approvalComment) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 验证订单状态（草稿状态可以审批）
            if (SaleOrderStatus.DRAFT != order.getOrderStatus()) {
                throw new ServiceException("订单状态不正确，当前状态：" + order.getOrderStatus() + "，无法审批");
            }

            // 记录审批信息
            Long approverId = LoginHelper.getUserId();
            String approverName = LoginHelper.getLoginUser().getNickname();

            // 更新订单状态和审批信息
            order.setOrderStatus(SaleOrderStatus.CONFIRMED);
            order.setApproverId(approverId);
            order.setApproverName(approverName);

            // 添加审批意见到备注
            if (StringUtils.isNotBlank(approvalComment)) {
                String newRemark = (order.getRemark() != null ? order.getRemark() : "") +
                    String.format(" [审批通过 - %s: %s]", approverName, approvalComment);
                order.setRemark(newRemark);
            }

            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // 执行审批通过后的业务逻辑
                executeApprovalPassedLogic(order);

                log.info("销售订单【{}】审批通过 - 审批人: {}",
                    order.getOrderCode(), approverName);
            }

            return result;
        } catch (Exception e) {
            log.error("审批销售订单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("审批订单失败：" + e.getMessage());
        }
    }

    /**
     * 审批驳回销售订单
     * ✅ 参考PurchaseOrderServiceImpl实现
     *
     * @param orderId 订单ID
     * @param rejectReason 驳回原因
     * @return 是否驳回成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectOrder(Long orderId, String rejectReason) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 验证订单状态（草稿状态可以拒绝）
            if (SaleOrderStatus.DRAFT != order.getOrderStatus()) {
                throw new ServiceException("订单状态不正确，当前状态：" + order.getOrderStatus() + "，无法拒绝");
            }

            // 验证拒绝原因
            if (StringUtils.isBlank(rejectReason)) {
                throw new ServiceException("拒绝原因不能为空");
            }

            // 记录审批信息
            Long approverId = LoginHelper.getUserId();
            String approverName = LoginHelper.getLoginUser().getNickname();

            // 更新订单状态为草稿，允许重新修改
            order.setOrderStatus(SaleOrderStatus.DRAFT);
            order.setApproverId(approverId);
            order.setApproverName(approverName);

            // 添加拒绝原因到备注
            String newRemark = (order.getRemark() != null ? order.getRemark() : "") +
                String.format(" [审批拒绝 - %s: %s]", approverName, rejectReason);
            order.setRemark(newRemark);

            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // 执行审批拒绝后的业务逻辑
                executeApprovalRejectedLogic(order, rejectReason);

                log.info("销售订单【{}】审批拒绝 - 审批人: {}, 拒绝原因: {}",
                    order.getOrderCode(), approverName, rejectReason);
            }

            return result;
        } catch (Exception e) {
            log.error("拒绝销售订单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("拒绝订单失败：" + e.getMessage());
        }
    }

    /**
     * 批量确认销售订单
     *
     * @param orderIds 订单ID集合
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchConfirmOrders(Collection<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                throw new ServiceException("订单ID集合不能为空");
            }

            int successCount = 0;
            for (Long orderId : orderIds) {
                try {
                    confirmOrder(orderId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量确认订单失败，订单ID：{}，错误：{}", orderId, e.getMessage());
                }
            }

            if (successCount == 0) {
                throw new ServiceException("批量确认失败，没有订单被成功确认");
            }

            log.info("批量确认销售订单完成：成功【{}】个，总计【{}】个", successCount, orderIds.size());
            return true;
        } catch (Exception e) {
            log.error("批量确认销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("批量确认销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 挂起销售订单
     *
     * @param orderId    订单ID
     * @param holdReason 挂起原因
     * @return 是否挂起成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean holdOrder(Long orderId, String holdReason) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态
            if (SaleOrderStatus.CONFIRMED != order.getOrderStatus()) {
                throw new ServiceException("只有已确认状态的订单才能挂起");
            }

            // 更新订单状态和挂起原因
            order.setOrderStatus(SaleOrderStatus.ON_HOLD);
            if (StringUtils.isNotBlank(holdReason)) {
                String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
                order.setRemark(currentRemark + " [挂起原因：" + holdReason + "]");
            }

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("挂起销售订单失败");
            }

            log.info("挂起销售订单成功：订单【{}】，原因【{}】", order.getOrderCode(), holdReason);
            return true;
        } catch (Exception e) {
            log.error("挂起销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("挂起销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 恢复挂起的销售订单
     *
     * @param orderId 订单ID
     * @return 是否恢复成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resumeOrder(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态
            if (SaleOrderStatus.ON_HOLD != order.getOrderStatus()) {
                throw new ServiceException("只有挂起状态的订单才能恢复");
            }

            // 恢复订单状态为已确认
            order.setOrderStatus(SaleOrderStatus.CONFIRMED);
            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("恢复销售订单失败");
            }

            log.info("恢复销售订单成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("恢复销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("恢复销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 取消销售订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, String cancelReason) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态
            if (SaleOrderStatus.CANCELLED == order.getOrderStatus() ||
                SaleOrderStatus.CLOSED == order.getOrderStatus()) {
                throw new ServiceException("订单已取消或已关闭，不能重复操作");
            }

            // 检查是否有关联的出库单
            if (saleOutboundService.existsByOrderId(orderId)) {
                throw new ServiceException("订单已有关联出库单，不能取消");
            }

            // 更新订单状态和取消原因
            order.setOrderStatus(SaleOrderStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(order.getRemark()) ? order.getRemark() : "";
                order.setRemark(currentRemark + " [取消原因：" + cancelReason + "]");
            }

            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("取消销售订单失败");
            }

            log.info("取消销售订单成功：订单【{}】，原因【{}】", order.getOrderCode(), cancelReason);
            return true;
        } catch (Exception e) {
            log.error("取消销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("取消销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 关闭销售订单
     *
     * @param orderId 订单ID
     * @return 是否关闭成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeOrder(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态
            if (SaleOrderStatus.FULLY_SHIPPED == order.getOrderStatus()) {
                throw new ServiceException("只有全部发货状态的订单才能关闭");
            }

            // 更新订单状态
            order.setOrderStatus(SaleOrderStatus.CLOSED);
            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("关闭销售订单失败");
            }

            log.info("关闭销售订单成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("关闭销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("关闭销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 完成销售订单（修改为关闭订单，因为没有COMPLETED状态）
     *
     * @param orderId 订单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeOrder(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态
            if (SaleOrderStatus.FULLY_SHIPPED != order.getOrderStatus()) {
                throw new ServiceException("只有全部发货状态的订单才能完成");
            }

            // 检查是否所有出库单都已完成
            if (!checkAllOutboundCompleted(orderId)) {
                throw new ServiceException("存在未完成的出库单，无法完成订单");
            }

            // 更新订单状态为已关闭（因为没有COMPLETED状态）
            order.setOrderStatus(SaleOrderStatus.CLOSED);


            int result = baseMapper.updateById(order);
            if (result <= 0) {
                throw new ServiceException("完成销售订单失败");
            }

            // 执行完成后的业务逻辑
            executeOrderCompletedLogic(order);

            log.info("完成销售订单成功：订单【{}】", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("完成销售订单失败：{}", e.getMessage(), e);
            throw new ServiceException("完成销售订单失败：" + e.getMessage());
        }
    }

    /**
     * 从明细汇总到主表
     *
     * @param orderId 订单ID
     * @return 是否汇总成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean summarizeFromItems(Long orderId) {
        try {
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // ✅ 使用标准的金额合计方法，符合DDD原则：聚合根Service调用子实体Mapper
            TaxCalculationResultBo totalAmount = itemMapper.calculateTotalAmount(orderId);

            if (totalAmount == null) {
                log.info("订单【{}】没有明细，跳过金额汇总", order.getOrderCode());
                // 清空订单金额字段
                order.setAmount(BigDecimal.ZERO);
                order.setAmountExclusiveTax(BigDecimal.ZERO);
                order.setTaxAmount(BigDecimal.ZERO);
            } else {
                // ✅ 将明细汇总结果更新到主订单
                order.setAmount(totalAmount.getAmount());
                order.setAmountExclusiveTax(totalAmount.getAmountExclusiveTax());
                order.setTaxAmount(totalAmount.getTaxAmount());

                log.info("订单【{}】金额汇总计算完成：不含税【{}】税额【{}】总金额【{}】",
                    order.getOrderCode(), totalAmount.getAmountExclusiveTax(), totalAmount.getTaxAmount(), totalAmount.getAmount());
            }

            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                log.info("销售订单明细汇总成功 - 订单: {}", order.getOrderCode());

            }

            return result;
        } catch (Exception e) {
            log.error("销售订单明细汇总失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("销售订单明细汇总失败：" + e.getMessage());
        }
    }

    /**
     * 检查所有出库单是否已完成
     */
    private boolean checkAllOutboundCompleted(Long orderId) {
        try {
            // 实现检查所有出库单是否已完成的逻辑
            List<SaleOutboundVo> outbounds = saleOutboundService.queryByOrderId(orderId);

            if (outbounds.isEmpty()) {
                log.debug("订单没有关联的出库单 - 订单ID: {}", orderId);
                return true; // 没有出库单认为已完成
            }

            // 检查所有出库单是否都已完成
            for (SaleOutboundVo outbound : outbounds) {
                if (outbound.getOutboundStatus() == null ||
                    SaleOutboundStatus.COMPLETED != outbound.getOutboundStatus()) {
                    log.debug("存在未完成的出库单 - 订单ID: {}, 出库单: {}, 状态: {}",
                        orderId, outbound.getOutboundCode(), outbound.getOutboundStatus());
                    return false;
                }
            }

            log.debug("所有出库单已完成 - 订单ID: {}, 出库单数量: {}", orderId, outbounds.size());
            return true;
        } catch (Exception e) {
            log.warn("检查出库单完成状态失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查库存是否充足
     */
    private boolean checkInventoryAvailable(Long orderId) {
        try {
            // 基于销售订单明细进行库存检查（遵循明细→批次的标准结构）
            List<SaleOrderItemVo> items = itemMapper.queryByOrderId(orderId);
            if (items.isEmpty()) {
                log.debug("订单没有明细，跳过库存检查 - 订单ID: {}", orderId);
                return true;
            }

            boolean allAvailable = true;
            for (SaleOrderItemVo item : items) {
                // 检查每个明细的库存可用性（基于明细的产品）
                // 查询实际库存可用量
                BigDecimal availableQuantity = inventoryService.getAvailableQuantity(item.getProductId(), null); // 不指定库位，查询总可用量

                if (availableQuantity == null) {
                    availableQuantity = BigDecimal.ZERO;
                }

                if (availableQuantity == null || availableQuantity.compareTo(item.getQuantity()) < 0) {
                    log.warn("库存不足 - 明细ID: {}, 产品ID: {}, 需求数量: {}, 可用数量: {}", item.getItemId(), item.getProductId(), item.getQuantity(), availableQuantity);
                    allAvailable = false;
                    // 继续检查其他明细，记录所有库存不足的情况
                }
            }

            if (allAvailable) {
                log.debug("库存检查通过 - 订单ID: {}", orderId);
            } else {
                log.warn("库存检查失败，存在库存不足 - 订单ID: {}", orderId);
            }

            return allAvailable;
        } catch (Exception e) {
            log.warn("检查库存可用性失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return false;
        }
    }

    /**
     * 执行订单完成后的业务逻辑
     */
    private void executeOrderCompletedLogic(SaleOrder order) {
        try {
            // 发送订单完成通知
            sendOrderCompletedNotification(order);

            // 更新客户信用记录
            updateCustomerCreditRecord(order);

            // 触发后续业务流程（如自动开票）
            triggerSubsequentProcess(order);

            log.info("订单完成后业务逻辑执行完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("执行订单完成后业务逻辑失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 发送订单完成通知
     */
    private void sendOrderCompletedNotification(SaleOrder order) {
        try {
            // 计算订单总金额
            BigDecimal orderAmount = calculateOrderTotalAmount(order.getOrderId());

            // 实现基础通知功能
            String notificationContent = String.format(
                "订单完成通知 - 订单号: %s, 客户: %s, 金额: %s, 完成时间: %s",
                order.getOrderCode(), order.getCustomerName(), orderAmount, LocalDateTime.now()
            );

            // 记录到系统日志
            log.info("订单完成通知: {}", notificationContent);


        } catch (Exception e) {
            log.warn("发送订单完成通知失败: {}", e.getMessage());
        }
    }

    /**
     * 更新客户信用记录
     */
    private void updateCustomerCreditRecord(SaleOrder order) {
        try {
            // 计算订单总金额
            BigDecimal orderAmount = calculateOrderTotalAmount(order.getOrderId());

            // 构建信用记录信息
            String creditRecord = String.format(
                "订单完成 - %s: %s, 金额: %s",
                LocalDate.now(), order.getOrderCode(), orderAmount
            );

            log.info("客户信用记录更新 - 客户: {}, 记录: {}", order.getCustomerName(), creditRecord);

            // 可以在客户备注中记录信用信息
            try {
                CompanyVo customer = companyService.queryById(order.getCustomerId());
                if (customer != null) {
                    log.debug("客户信用记录 - 客户ID: {}, 客户名称: {}, 交易记录: {}",
                        customer.getCompanyId(), customer.getCompanyName(), creditRecord);
                }
            } catch (Exception ex) {
                log.warn("获取客户信息失败: {}", ex.getMessage());
            }


        } catch (Exception e) {
            log.warn("更新客户信用记录失败: {}", e.getMessage());
        }
    }

    /**
     * 计算订单总金额
     *
     * @param orderId 订单ID
     * @return 订单总金额
     */
    private BigDecimal calculateOrderTotalAmount(Long orderId) {
        try {
            // 获取订单明细列表
            List<SaleOrderItemVo> items = itemMapper.queryByOrderId(orderId);

            // 计算总金额
            BigDecimal totalAmount = items.stream()
                .map(item -> {
                    BigDecimal itemAmount = item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO;
                    if (itemAmount.equals(BigDecimal.ZERO) && item.getPrice() != null && item.getQuantity() != null) {
                        // 如果明细没有金额，则用价格*数量计算
                        itemAmount = item.getPrice().multiply(item.getQuantity());
                    }
                    return itemAmount;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("计算订单总金额 - 订单ID: {}, 明细数量: {}, 总金额: {}",
                orderId, items.size(), totalAmount);

            return totalAmount;
        } catch (Exception e) {
            log.warn("计算订单总金额失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 触发后续业务流程
     */
    private void triggerSubsequentProcess(SaleOrder order) {
        try {
            // 创建应收账款
            createReceivableFromOrder(order);

            // 自动开具发票（可选）
            // autoCreateInvoice(order);

            // 更新客户统计信息
            updateCustomerStatistics(order);

            log.info("后续业务流程触发完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.error("触发后续业务流程失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从销售订单创建应收账款
     */
    private void createReceivableFromOrder(SaleOrder order) {
        try {
            // 调用应收账款服务创建应收账款
            try {
                Long receivableId = finArReceivableService.generateFromSaleOrder(
                    order.getOrderId(),
                    order.getOrderCode(),
                    order.getCustomerId(),
                    order.getCustomerName(),
                    calculateOrderTotalAmount(order.getOrderId()), // 使用已有的计算方法
                    order.getOrderDate().plusDays(30), // 默认30天付款期
                    order.getHandlerId(),
                    order.getHandlerName()
                );

                if (receivableId != null) {
                    log.info("从销售订单创建应收账款成功 - 订单: {}, 应收账款ID: {}",
                        order.getOrderCode(), receivableId);
                } else {
                    log.warn("从销售订单创建应收账款失败 - 订单: {}", order.getOrderCode());
                }
            } catch (Exception e) {
                log.error("从销售订单创建应收账款异常 - 订单: {}, 错误: {}",
                    order.getOrderCode(), e.getMessage(), e);
                // 不抛出异常，避免影响主流程
            }
            log.info("从销售订单创建应收账款功能待完善 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.error("从销售订单创建应收账款异常 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
        }
    }

    /**
     * 从销售订单生成应收单
     *
     * @param orderId 销售订单ID
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateReceivableFromOrder(Long orderId) {
        try {
            if (orderId == null) {
                throw new ServiceException("销售订单ID不能为空");
            }

            // 获取销售订单信息
            SaleOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("销售订单不存在");
            }

            // 校验订单状态（只有确认状态的订单才能生成应收单）
            if (SaleOrderStatus.CONFIRMED != order.getOrderStatus()) {
                throw new ServiceException("只有已确认的销售订单才能生成应收单");
            }

            // 检查是否已经生成过应收单
            if (finArReceivableService.existsByOrderId(orderId)) {
                throw new ServiceException("该销售订单已生成应收单，不能重复生成");
            }

            // TODO: [销售订单金额计算逻辑实现] - 优先级: HIGH - 参考文档: docs/design/README_FINANCE.md
            // 当前使用临时方法 calculateOrderTotalAmount，需要完善为完整的金额计算逻辑：
            // 1. 遍历 erp_sale_order_item 表，获取所有订单明细
            // 2. 计算每行金额：quantity × price_exclusive_tax（不含税单价）
            // 3. 汇总所有明细的不含税金额：sum(amount_exclusive_tax)
            // 4. 计算税额：total_exclusive_tax × tax_rate
            // 5. 计算含税总额：total_exclusive_tax + total_tax_amount
            // 6. 考虑订单级别的折扣率：final_amount = total_inclusive_tax × (1 - discount_rate)
            // 7. 使用 BigDecimal 确保精度，设置合理的舍入模式
            BigDecimal receivableAmount = calculateOrderTotalAmount(orderId);
            if (receivableAmount == null || receivableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("销售订单金额无效，无法生成应收单");
            }

            // 计算到期日期（默认30天后到期）
            LocalDate dueDate = LocalDate.now().plusDays(30);

            // 调用应收账款服务生成应收单
            // ✅ 修复：通过UserService获取创建人姓名
            String createByName = order.getCreateBy() != null ?
                userService.selectNicknameById(order.getCreateBy()) : null;
            Long receivableId = finArReceivableService.generateFromSaleOrder(
                orderId, order.getOrderCode(),
                order.getCustomerId(), order.getCustomerName(),
                receivableAmount, dueDate,
                order.getCreateBy(), createByName
            );

            if (receivableId == null) {
                throw new ServiceException("生成应收单失败");
            }

            log.info("从销售订单生成应收单成功 - 订单: {}, 应收单ID: {}, 金额: {}",
                order.getOrderCode(), receivableId, receivableAmount);
            return true;
        } catch (Exception e) {
            log.error("从销售订单生成应收单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("从销售订单生成应收单失败：" + e.getMessage());
        }
    }

    /**
     * 更新客户统计信息
     */
    private void updateCustomerStatistics(SaleOrder order) {
        try {
            // 实现客户统计信息更新：累计销售金额、订单数量、最后交易时间
            log.debug("更新客户统计信息 - 订单: {}, 客户: {}", order.getOrderCode(), order.getCustomerName());
        } catch (Exception e) {
            log.error("更新客户统计信息失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
        }
    }

    /**
     * 销售订单财务对账
     *
     * @param saleOrderId 销售订单ID
     * @return 对账结果
     */
    @Override
    public Map<String, Object> reconcileSaleOrder(Long saleOrderId) {
        try {
            log.info("开始销售订单财务对账 - 订单ID: {}", saleOrderId);

            Map<String, Object> result = new java.util.HashMap<>();

            // 销售订单信息
            SaleOrderVo saleOrder = queryById(saleOrderId);
            if (saleOrder == null) {
                throw new ServiceException("销售订单不存在");
            }
            result.put("saleOrder", saleOrder);

            // 查询关联的出库单信息
            try {
                List<SaleOutboundVo> outbounds = saleOutboundService.queryByOrderId(saleOrderId);
                result.put("outbounds", outbounds != null ? outbounds : new ArrayList<>());
                log.debug("查询订单关联出库单 - 订单ID: {}, 出库单数量: {}",
                    saleOrderId, outbounds != null ? outbounds.size() : 0);
            } catch (Exception e) {
                log.warn("查询订单关联出库单失败 - 订单ID: {}, 错误: {}", saleOrderId, e.getMessage());
                result.put("outbounds", new ArrayList<>());
            }

            // 查询生成的应收单信息
            try {
                List<FinArReceivableVo> receivables = finArReceivableService.queryBySourceId(saleOrderId, SourceType.SALE_ORDER);
                result.put("receivables", receivables != null ? receivables : new ArrayList<>());
                log.debug("查询订单关联应收单 - 订单ID: {}, 应收单数量: {}",
                    saleOrderId, receivables != null ? receivables.size() : 0);
            } catch (Exception e) {
                log.warn("查询订单关联应收单失败 - 订单ID: {}, 错误: {}", saleOrderId, e.getMessage());
                result.put("receivables", new ArrayList<>());
            }

            // 相关的收款单信息
            // TODO: 查询相关的收款单信息
            // List<FinArReceiptOrderVo> receipts = finArReceiptOrderService.queryByOrderId(saleOrderId);
            // result.put("receipts", receipts);
            result.put("receipts", new ArrayList<>());

            // 核销记录信息
            // TODO: 查询核销记录信息
            // List<FinArReceiptReceivableLinkVo> links = finArReceiptReceivableLinkService.queryByOrderId(saleOrderId);
            // result.put("links", links);
            result.put("links", new ArrayList<>());

            // 账户流水信息
            // TODO: 查询账户流水信息
            // List<FinAccountLedgerVo> ledgers = finAccountLedgerService.queryByOrderId(saleOrderId);
            // result.put("ledgers", ledgers);
            result.put("ledgers", new ArrayList<>());

            // 对账结果分析
            Map<String, Object> analysis = new java.util.HashMap<>();

            // 计算订单金额（通过明细汇总）
            BigDecimal orderAmount = calculateOrderTotalAmount(saleOrderId);
            analysis.put("orderAmount", orderAmount);

            // 计算应收金额
            BigDecimal receivableAmount = BigDecimal.ZERO;
            try {
                List<FinArReceivableVo> receivables = finArReceivableService.queryBySourceId(saleOrderId, SourceType.SALE_ORDER);
                receivableAmount = receivables.stream()
                    .filter(r -> !"CANCELLED".equals(r.getReceivableStatus()))
                    .map(r -> r.getAmount() != null ? r.getAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            } catch (Exception e) {
                log.warn("计算应收金额失败 - 订单ID: {}, 错误: {}", saleOrderId, e.getMessage());
            }
            analysis.put("receivableAmount", receivableAmount);

            // 计算收款金额
            BigDecimal receiptAmount = BigDecimal.ZERO;
            try {
                receiptAmount = financialReconciliationService.calculateOrderReceivedAmount(saleOrderId);
            } catch (Exception e) {
                log.warn("计算收款金额失败 - 订单ID: {}, 错误: {}", saleOrderId, e.getMessage());
            }
            analysis.put("receiptAmount", receiptAmount);

            // 计算余额
            BigDecimal balanceAmount = receivableAmount.subtract(receiptAmount);
            analysis.put("balanceAmount", balanceAmount);

            // 计算对账状态
            String reconcileStatus = "PENDING";
            if (orderAmount.compareTo(receivableAmount) == 0) {
                if (receiptAmount.compareTo(receivableAmount) == 0) {
                    reconcileStatus = "COMPLETED";
                } else if (receiptAmount.compareTo(BigDecimal.ZERO) > 0) {
                    reconcileStatus = "PARTIAL";
                }
            } else {
                reconcileStatus = "INCONSISTENT";
            }
            analysis.put("reconcileStatus", reconcileStatus);

            result.put("analysis", analysis);

            log.debug("对账分析完成 - 订单ID: {}, 订单金额: {}, 应收金额: {}, 收款金额: {}, 余额: {}, 状态: {}",
                saleOrderId, orderAmount, receivableAmount, receiptAmount, balanceAmount, reconcileStatus);

            result.put("success", true);
            result.put("message", "销售订单财务对账查询成功");

            log.info("销售订单财务对账完成 - 订单ID: {}", saleOrderId);
            return result;
        } catch (Exception e) {
            log.error("销售订单财务对账失败 - 订单ID: {}, 错误: {}", saleOrderId, e.getMessage(), e);
            throw new ServiceException("销售订单财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 创建出库单
     *
     * @param orderId 销售订单ID
     * @return 是否创建成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createOutbound(Long orderId) {
        try {
            if (orderId == null) {
                throw new ServiceException("订单ID不能为空");
            }
            // 检查订单是否存在
            SaleOrderVo orderVo = baseMapper.selectVoById(orderId);
            if (orderVo == null) {
                throw new ServiceException("销售订单不存在");
            }
            // 检查订单状态
            if (SaleOrderStatus.CONFIRMED != orderVo.getOrderStatus()) {
                throw new ServiceException("只有已确认的订单才能创建出库单");
            }
            // 检查订单明细
            List<SaleOrderItemVo> items = itemMapper.queryByOrderId(orderId);
            if (items.isEmpty()) {
                throw new ServiceException("销售订单没有明细，不能创建出库单");
            }
            // 检查是否已有出库单
            if (saleOutboundService.existsByOrderId(orderId)) {
                throw new ServiceException("该订单已有出库单，不能重复创建");
            }
            // 检查库存是否充足
            if (!checkInventoryAvailable(orderId)) {
                throw new ServiceException("库存不足，无法创建出库单");
            }
            orderVo.setItems(items);
            // 调用出库单服务创建出库单
            boolean result = saleOutboundService.createFromSaleOrder(orderVo);
            if (!result) {
                throw new ServiceException("创建出库单失败");
            }
            log.info("创建出库单成功 - 订单: {}", orderVo.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("创建出库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("创建出库单失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建出库单
     *
     * @param orderIds 销售订单ID列表
     * @return 创建成功的数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchCreateOutbound(List<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                throw new ServiceException("订单ID列表不能为空");
            }

            int successCount = 0;
            for (Long orderId : orderIds) {
                try {
                    Boolean result = createOutbound(orderId);
                    if (result) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.warn("批量创建出库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
                    // 继续处理其他订单，不中断整个批量操作
                }
            }

            log.info("批量创建出库单完成 - 总数: {}, 成功: {}", orderIds.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量创建出库单失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量创建出库单失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(SaleOrderBo bo) {
        // 填充客户信息
//        if (bo.getCustomerId() != null) {
//            CompanyVo customer = companyService.queryById(bo.getCustomerId());
//            if (customer != null) {
//                bo.setCustomerName(customer.getCompanyName());
//            }
//        }
        //填充责任人信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            if (bo.getOrderId() == null) {
                // 设置经办人（每次更新都更新）
                bo.setHandlerId(loginUser.getUserId());
                bo.setHandlerName(loginUser.getNickname());
            }
        }
    }

    /**
     * 填充明细数据
     */
    private void fillItemsData(List<SaleOrderItemBo> items, Long orderId) {
        for (SaleOrderItemBo item : items) {
            item.setOrderId(orderId);

            // 填充产品信息
            if (item.getProductId() != null) {
                ProductVo product = productService.queryById(item.getProductId());
                if (product != null) {
                    item.setProductCode(product.getProductCode());
                    item.setProductName(product.getProductName());
                    item.setUnitId(product.getUnitId());
                    item.setUnitCode(product.getUnitCode());
                    item.setUnitName(product.getUnitName());

                    // 如果没有设置价格，使用产品的销售价格
                    if (item.getPrice() == null && product.getSalePrice() != null) {
                        item.setPrice(product.getSalePrice());
                    }
                    if (item.getPriceExclusiveTax() == null && product.getSalePriceExclusiveTax() != null) {
                        item.setPriceExclusiveTax(product.getSalePriceExclusiveTax());
                    }
                    if (item.getTaxRate() == null && product.getSaleTaxRate() != null) {
                        item.setTaxRate(product.getSaleTaxRate());
                    }
                }
            }
        }
    }

    /**
     * 计算明细金额（价税分离）
     */
    private void calculateItemAmounts(List<SaleOrderItemBo> items) {
        for (SaleOrderItemBo item : items) {
            if (item.getQuantity() != null && item.getPrice() != null) {
                BigDecimal quantity = item.getQuantity();
                BigDecimal price = item.getPrice(); // 含税价
                BigDecimal taxRate = item.getTaxRate() != null ? item.getTaxRate() : BigDecimal.ZERO;

                // 计算含税金额
                BigDecimal amount = price.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                item.setAmount(amount);

                // 计算不含税价格和金额
                if (taxRate.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
                    BigDecimal priceExclusiveTax = price.divide(divisor, 4, RoundingMode.HALF_UP);
                    BigDecimal amountExclusiveTax = priceExclusiveTax.multiply(quantity).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal taxAmount = amount.subtract(amountExclusiveTax);

                    item.setPriceExclusiveTax(priceExclusiveTax);
                    item.setAmountExclusiveTax(amountExclusiveTax);
                    item.setTaxAmount(taxAmount);
                } else {
                    // 无税情况
                    item.setPriceExclusiveTax(price);
                    item.setAmountExclusiveTax(amount);
                    item.setTaxAmount(BigDecimal.ZERO);
                }
            }
        }
    }

    /**
     * 更新主表汇总金额
     */
    private void updateTotalAmounts(Long orderId) {
        // 计算明细总金额和总数量
        List<SaleOrderItemVo> items = itemMapper.queryByOrderId(orderId);

        BigDecimal totalQuantity = items.stream()
            .map(item -> item.getQuantity() != null ? item.getQuantity() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAmount = items.stream()
            .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalAmountExclusiveTax = items.stream()
            .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalTaxAmount = items.stream()
            .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // TODO 需完善 更新主表汇总字段（已启用持久化到数据库）
        SaleOrder update = new SaleOrder();
        update.setOrderId(orderId);
//        update.setTotalQuantity(totalQuantity);
//        update.setTotalAmount(totalAmount);
//        update.setTotalAmountExclusiveTax(totalAmountExclusiveTax);
//        update.setTotalTaxAmount(totalTaxAmount);

        // 执行更新
        boolean updateResult = baseMapper.updateById(update) > 0;
        if (updateResult) {
            log.debug("更新订单汇总数据成功 - 订单ID: {}, 总数量: {}, 含税总额: {}, 不含税总额: {}, 税额: {}",
                orderId, totalQuantity, totalAmount, totalAmountExclusiveTax, totalTaxAmount);
        } else {
            log.warn("更新订单汇总数据失败 - 订单ID: {}", orderId);
        }
    }

    /**
     * 校验状态流转合法性
     */
    private boolean isValidStatusTransition(SaleOrderStatus fromStatus, SaleOrderStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return true;
        }

        // 定义合法的状态流转
        switch (fromStatus) {
            case DRAFT:
                return toStatus == SaleOrderStatus.CONFIRMED ||
                    toStatus == SaleOrderStatus.DRAFT;
            case CONFIRMED:
                return toStatus == SaleOrderStatus.PARTIALLY_SHIPPED ||
                    toStatus == SaleOrderStatus.FULLY_SHIPPED ||
                    toStatus == SaleOrderStatus.ON_HOLD ||
                    toStatus == SaleOrderStatus.CANCELLED;
            case PARTIALLY_SHIPPED:
                return toStatus == SaleOrderStatus.FULLY_SHIPPED ||
                    toStatus == SaleOrderStatus.CANCELLED;
            case FULLY_SHIPPED:
                return toStatus == SaleOrderStatus.CLOSED;
            case ON_HOLD:
                return toStatus == SaleOrderStatus.CONFIRMED ||
                    toStatus == SaleOrderStatus.CANCELLED;
            case CANCELLED:
            case CLOSED:
                return toStatus == fromStatus; // 终态，不能再变更
            default:
                return false;
        }
    }

    // ================================ 审批流程辅助方法 ================================

    /**
     * 准备审批流程变量
     * ✅ 参考PurchaseOrderServiceImpl实现
     */
    private Map<String, Object> prepareApprovalVariables(SaleOrder order) {
        Map<String, Object> variables = new HashMap<>();

        // 基本订单信息
        variables.put("orderCode", order.getOrderCode());
        variables.put("customerId", order.getCustomerId());
        variables.put("customerName", order.getCustomerName());
        variables.put("orderAmount", order.getAmount());
        variables.put("orderDate", order.getOrderDate());
        variables.put("deliveryDate", order.getDeliveryDate());

        // 审批路由参数
        BigDecimal orderAmount = order.getAmount() != null ? order.getAmount() : BigDecimal.ZERO;
        variables.put("needHighLevelApproval", orderAmount.compareTo(new BigDecimal("100000")) > 0);
        variables.put("isUrgentOrder", order.getDeliveryDate() != null &&
            order.getDeliveryDate().isBefore(LocalDate.now().plusDays(3)));

        // 申请人信息
        variables.put("applicantId", order.getCreateBy());
        variables.put("applicantName", order.getCreateByName());
        variables.put("applicantDept", order.getCompanyName());

        log.debug("准备销售订单审批变量 - 订单: {}, 金额: {}, 需要高级审批: {}",
            order.getOrderCode(), orderAmount, variables.get("needHighLevelApproval"));

        return variables;
    }

    /**
     * 启动审批流程
     * ✅ 参考PurchaseOrderServiceImpl实现，集成warm-flow工作流引擎
     */
    private void startApprovalProcess(SaleOrder order, Map<String, Object> variables) {
        try {
            // TODO: [warm-flow工作流引擎集成] - 优先级: HIGH - 参考文档: docs/design/README_FLOW.md
            // 需要完善工作流引擎集成逻辑：
            // 1. 准备审批流程参数：订单金额、客户信息、紧急程度等
            // 2. 调用 warm-flow API 启动审批流程实例
            // 3. 获取流程实例ID，但不存储在业务表中（遵循外部流程控制模式）
            // 4. 设置审批路由规则：金额阈值、部门层级、重要客户等
            // 5. 配置审批超时处理和自动升级机制

            log.info("启动销售订单审批流程 - 订单: {}", order.getOrderCode());

            // 示例代码（待集成warm-flow时启用）：
            // String processDefinitionKey = "sale_order_approval";
            // String businessKey = order.getOrderCode();
            // warmFlowService.startProcess(processDefinitionKey, businessKey, variables);

            // 注意：warm-flow 采用外部流程控制业务表状态的架构模式
            // 流程实例ID等内部字段由流程引擎管理，业务表不存储
        } catch (Exception e) {
            log.error("启动审批流程失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            throw new ServiceException("启动审批流程失败：" + e.getMessage());
        }
    }

    /**
     * 发送审批通知
     */
    private void sendApprovalNotification(SaleOrder order) {
        try {
            // TODO: [审批通知机制] - 优先级: MEDIUM - 参考文档: docs/design/README_NOTIFICATION.md
            // 实现审批通知逻辑：
            // 1. 根据订单金额和客户重要性确定审批人
            // 2. 发送邮件/短信/系统消息通知
            // 3. 记录通知发送日志
            // 4. 支持通知失败重试机制

            log.info("发送审批通知 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("发送审批通知失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 执行审批通过后的业务逻辑
     */
    private void executeApprovalPassedLogic(SaleOrder order) {
        try {
            // 发送审批通过通知
            sendApprovalPassedNotification(order);

            // 更新相关业务状态
            updateRelatedBusinessStatus(order, "APPROVED");

            // TODO: [审批通过后的自动化流程] - 优先级: MEDIUM
            // 可选的自动化操作（根据业务配置决定是否执行）：
            // 1. 自动创建销售出库单
            // 2. 自动生成应收单
            // 3. 发送确认通知给客户
            // if (isAutoCreateOutboundEnabled()) {
            //     createOutbound(order.getOrderId());
            // }

            log.info("审批通过后业务逻辑执行完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("执行审批通过后业务逻辑失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 执行审批拒绝后的业务逻辑
     */
    private void executeApprovalRejectedLogic(SaleOrder order, String rejectReason) {
        try {
            // 发送审批拒绝通知
            sendApprovalRejectedNotification(order, rejectReason);

            // 更新相关业务状态
            updateRelatedBusinessStatus(order, "REJECTED");

            // 清理相关资源
            cleanupRelatedResources(order);

            log.info("审批拒绝后业务逻辑执行完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("执行审批拒绝后业务逻辑失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 发送审批通过通知
     */
    private void sendApprovalPassedNotification(SaleOrder order) {
        // TODO: 实现审批通过通知
        log.info("发送审批通过通知 - 订单: {}", order.getOrderCode());
    }

    /**
     * 发送审批拒绝通知
     */
    private void sendApprovalRejectedNotification(SaleOrder order, String rejectReason) {
        // TODO: 实现审批拒绝通知
        log.info("发送审批拒绝通知 - 订单: {}, 拒绝原因: {}", order.getOrderCode(), rejectReason);
    }

    /**
     * 更新相关业务状态
     */
    private void updateRelatedBusinessStatus(SaleOrder order, String status) {
        // TODO: 更新相关业务状态
        log.debug("更新相关业务状态 - 订单: {}, 状态: {}", order.getOrderCode(), status);
    }

    /**
     * 清理相关资源
     */
    private void cleanupRelatedResources(SaleOrder order) {
        // TODO: 清理相关资源
        log.debug("清理相关资源 - 订单: {}", order.getOrderCode());
    }

}
