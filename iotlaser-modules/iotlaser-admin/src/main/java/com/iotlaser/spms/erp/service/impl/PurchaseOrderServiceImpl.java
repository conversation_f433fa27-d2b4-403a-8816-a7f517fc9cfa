package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseOrder;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.BomInventoryAnalysisVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseRequirementVo;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.mapper.PurchaseOrderItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseOrderMapper;
import com.iotlaser.spms.erp.service.IBomInventoryAnalysisService;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import com.iotlaser.spms.wms.service.IInboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_ORDER_CODE;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 采购订单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseOrderServiceImpl implements IPurchaseOrderService {

    private final PurchaseOrderMapper baseMapper;
    private final PurchaseOrderItemMapper itemMapper;  // ✅ DDD正确：聚合根直接管理子实体Mapper

    // TODO: [DDD重构-跨聚合调用] - 优先级: MEDIUM - 参考文档 docs/design/README_FLOW.md
    // 按照严格的DDD原则，聚合根Service不应该直接依赖其他聚合根Service
    // 当前保留这些依赖以维持业务功能完整性，后续需要重构为：
    // 1. 使用领域事件处理跨聚合的业务协调（如订单确认后创建入库单）
    // 2. 使用应用服务层协调多个聚合根的操作
    // 3. 将只读查询操作移到查询服务中
    private final IPurchaseInboundService purchaseInboundService;  // TEMP: 跨聚合调用
    private final IInboundService inboundService;                  // TEMP: 跨聚合调用
    private final ICompanyService companyService;                  // TEMP: 基础数据查询
    private final IProductService productService;                  // TEMP: 基础数据查询
    private final Gen gen;
    private final IBomInventoryAnalysisService bomInventoryAnalysisService;  // TEMP: 跨聚合调用

    /**
     * 查询采购订单
     *
     * @param orderId 主键
     * @return 采购订单
     */
    @Override
    public PurchaseOrderVo queryById(Long orderId) {
        return baseMapper.selectVoById(orderId);
    }

    /**
     * 分页查询采购订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购订单分页列表
     */
    @Override
    public TableDataInfo<PurchaseOrderVo> queryPageList(PurchaseOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseOrder> lqw = buildQueryWrapper(bo);
        Page<PurchaseOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购订单列表
     *
     * @param bo 查询条件
     * @return 采购订单列表
     */
    @Override
    public List<PurchaseOrderVo> queryList(PurchaseOrderBo bo) {
        LambdaQueryWrapper<PurchaseOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseOrder> buildQueryWrapper(PurchaseOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseOrder::getOrderId);
        lqw.eq(StringUtils.isNotBlank(bo.getOrderCode()), PurchaseOrder::getOrderCode, bo.getOrderCode());
        lqw.eq(bo.getSourceId() != null, PurchaseOrder::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), PurchaseOrder::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(PurchaseOrder::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, PurchaseOrder::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), PurchaseOrder::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(PurchaseOrder::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getSupplierId() != null, PurchaseOrder::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseOrder::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getOrderDate() != null, PurchaseOrder::getOrderDate, bo.getOrderDate());
        if (bo.getOrderStatus() != null) {
            lqw.eq(PurchaseOrder::getOrderStatus, bo.getOrderStatus().getValue());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseOrder::getStatus, bo.getStatus());
        lqw.between(params.get("beginOrderDate") != null && params.get("endOrderDate") != null,
            PurchaseOrder::getOrderDate, params.get("beginOrderDate"), params.get("endOrderDate"));
        return lqw;
    }

    /**
     * 新增采购订单
     *
     * @param bo 采购订单
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrderVo insertByBo(PurchaseOrderBo bo) {
        try {
            // 生成订单编码
            if (StringUtils.isEmpty(bo.getOrderCode())) {
                bo.setOrderCode(gen.code(ERP_PURCHASE_ORDER_CODE));
            }
            // 2设置初始状态
            if (bo.getOrderStatus() == null) {
                bo.setOrderStatus(PurchaseOrderStatus.DRAFT);
            }
            // 设置下单日期
            if (bo.getOrderDate() == null) {
                bo.setOrderDate(LocalDate.now());
            }
            // 填充冗余字段
            fillRedundantFields(bo);
            // 转换为实体并校验
            PurchaseOrder add = MapstructUtils.convert(bo, PurchaseOrder.class);
            validEntityBeforeSave(add);

            // 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增采购订单失败");
            } else {
                if (add.getSourceType() == null || add.getSourceType() == SourceType.PURCHASE_ORDER) {
                    add.setSourceId(add.getOrderId());
                    add.setSourceCode(add.getOrderCode());
                    add.setSourceType(SourceType.PURCHASE_ORDER);
                }
                if (add.getDirectSourceType() == null || add.getDirectSourceType() == DirectSourceType.PURCHASE_ORDER) {
                    add.setDirectSourceId(add.getOrderId());
                    add.setDirectSourceCode(add.getOrderCode());
                    add.setDirectSourceType(DirectSourceType.PURCHASE_ORDER);
                }
                baseMapper.updateById(add);
            }

            log.info("新增采购订单成功：{}", add.getOrderCode());
            return MapstructUtils.convert(add, PurchaseOrderVo.class);
        } catch (Exception e) {
            log.error("新增采购订单失败：{}", e.getMessage(), e);
            throw new ServiceException("新增采购订单失败：" + e.getMessage());
        }
    }

    /**
     * 修改采购订单
     *
     * @param bo 采购订单
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrderVo updateByBo(PurchaseOrderBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);
            // 转换为实体并校验
            PurchaseOrder update = MapstructUtils.convert(bo, PurchaseOrder.class);
            validEntityBeforeSave(update);
            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改采购订单失败");
            }
            log.info("修改采购订单成功：{}", update.getOrderCode());
            return MapstructUtils.convert(update, PurchaseOrderVo.class);
        } catch (Exception e) {
            log.error("修改采购订单失败：{}", e.getMessage(), e);
            throw new ServiceException("修改采购订单失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseOrder entity) {
        // 校验订单编码唯一性
        if (StringUtils.isNotBlank(entity.getOrderCode())) {
            LambdaQueryWrapper<PurchaseOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseOrder::getOrderCode, entity.getOrderCode());
            if (entity.getOrderId() != null) {
                wrapper.ne(PurchaseOrder::getOrderId, entity.getOrderId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("采购订单编码已存在：" + entity.getOrderCode());
            }
        }
        // 校验状态流转合法性
        if (entity.getOrderId() != null) {
            PurchaseOrder existing = baseMapper.selectById(entity.getOrderId());
            if (existing != null && !isValidStatusTransition(existing.getOrderStatus(), entity.getOrderStatus())) {
                throw new ServiceException("状态流转不合法");
            }
        }
        // 校验供应商状态
        if (entity.getSupplierId() != null) {
            CompanyVo supplier = companyService.queryById(entity.getSupplierId());
            if (supplier == null) {
                throw new ServiceException("供应商不存在");
            }
            // TODO: 可以添加供应商状态校验
        }
    }

    /**
     * 校验并批量删除采购订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验订单状态，只有草稿状态的订单才能删除
            List<PurchaseOrder> orders = baseMapper.selectByIds(ids);
            for (PurchaseOrder order : orders) {
                if (PurchaseOrderStatus.DRAFT != order.getOrderStatus()) {
                    throw new ServiceException("采购订单【" + order.getOrderCode() + "】状态为【" + order.getOrderStatus() + "】，不允许删除");
                }

                // 检查是否有关联的入库单
                if (purchaseInboundService.existsByOrderId(order.getOrderId())) {
                    throw new ServiceException("采购订单【" + order.getOrderCode() + "】已有关联入库单，不允许删除");
                }

                // 检查是否有关联的发票
                checkRelatedInvoices(order.getOrderId());

                // 级联删除采购订单明细
                LambdaQueryWrapper<PurchaseOrderItem> lqw = Wrappers.lambdaQuery();
                lqw.eq(PurchaseOrderItem::getOrderId, order.getOrderId());
                int delete = itemMapper.delete(lqw);
                if (delete > 0) {
                    log.info("级联删除采购订单明细，订单：{}，明细数量：{}", order.getOrderCode(), delete);
                }

                log.info("删除采购订单校验通过：{}", order.getOrderCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除采购订单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除采购订单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除采购订单失败：" + e.getMessage());
        }
    }

    /**
     * 确认采购订单
     *
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmOrder(Long orderId) {
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("采购订单不存在");
        }

        // 校验订单状态
        if (PurchaseOrderStatus.DRAFT != order.getOrderStatus()) {
            throw new ServiceException("只有草稿状态的订单才能确认");
        }

        // 校验订单明细
        List<PurchaseOrderItemVo> items = itemMapper.selectListByOrderId(orderId);
        if (items.isEmpty()) {
            throw new ServiceException("订单明细不能为空");
        }

        for (PurchaseOrderItemVo item : items) {
            if (item.getQuantity() == null || item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("订单明细【" + item.getProductName() + "】采购数量不能小于等于0");
            }
        }

        // TODO: 重要功能 - 工作流审批机制
        /*if (needApproval(order)) {
            return submitForApproval(order);
        }*/

        // 更新订单状态
        order.setOrderStatus(PurchaseOrderStatus.CONFIRMED);
        int result = baseMapper.updateById(order);

        // TODO: [确认采购订单后，触发入库流程] - 参考文档 docs/design/README_FLOW.md
        // 确认成功后，应调用采购入库服务，为该订单创建对应的采购入库单。
        // if (result > 0) {
        //     purchaseInboundService.createFromPurchaseOrder(order);
        // }

        return result > 0;
    }

    /**
     * 批量确认采购订单
     *
     * @param orderIds 订单ID集合
     * @return 是否确认成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchConfirmOrders(Collection<Long> orderIds) {
        for (Long orderId : orderIds) {
            confirmOrder(orderId);
        }
        return true;
    }

    /**
     * 取消采购订单
     *
     * @param orderId 订单ID
     * @param reason  取消原因
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(Long orderId, String reason) {
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("采购订单不存在");
        }

        // 校验订单状态，只有草稿和已确认状态的订单才能取消
        if (PurchaseOrderStatus.DRAFT != order.getOrderStatus()
            && PurchaseOrderStatus.CONFIRMED != order.getOrderStatus()) {
            throw new ServiceException("只有草稿或已确认状态的订单才能取消");
        }

        // 如果是已确认状态，需要检查是否已有收货记录
        if (PurchaseOrderStatus.CONFIRMED == order.getOrderStatus()) {
            List<PurchaseOrderItemVo> items = itemMapper.selectListByOrderId(orderId);
            for (PurchaseOrderItemVo item : items) {
                if (item.getFinishQuantity() != null && item.getFinishQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    throw new ServiceException("订单已有收货记录，无法取消");
                }
            }
        }

        // 更新订单状态和取消原因
        order.setOrderStatus(PurchaseOrderStatus.CANCELLED);
        if (StringUtils.isNotBlank(reason)) {
            order.setRemark(order.getRemark() + " [取消原因：" + reason + "]");
        }
        return baseMapper.updateById(order) > 0;
    }

    /**
     * 关闭采购订单
     *
     * @param orderId 订单ID
     * @return 是否关闭成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeOrder(Long orderId) {
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("采购订单不存在");
        }

        // 校验订单状态，只有已收货状态的订单才能关闭
        if (PurchaseOrderStatus.FULLY_RECEIVED != order.getOrderStatus()
            && PurchaseOrderStatus.PARTIALLY_RECEIVED != order.getOrderStatus()) {
            throw new ServiceException("只有已收货状态的订单才能关闭");
        }

        // 更新订单状态
        order.setOrderStatus(PurchaseOrderStatus.CLOSED);
        return baseMapper.updateById(order) > 0;
    }

    /**
     * 更新订单收货状态
     * ✅ 修正：基于产品ID关联，支持分批入库的正确业务逻辑
     *
     * @param orderId        订单ID
     * @param finishQuantity 已收货数量映射 (productId -> quantity)
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReceivedStatus(Long orderId, Map<Long, BigDecimal> finishQuantity) {
        PurchaseOrder order = baseMapper.selectById(orderId);
        if (order == null) {
            throw new ServiceException("采购订单不存在");
        }

        // 校验订单状态
        if (PurchaseOrderStatus.CONFIRMED != order.getOrderStatus()
            && PurchaseOrderStatus.PARTIALLY_RECEIVED != order.getOrderStatus()) {
            throw new ServiceException("订单状态不正确，无法更新收货状态");
        }

        List<PurchaseOrderItemVo> items = itemMapper.selectListByOrderId(orderId);

        boolean allFullyReceived = true;
        boolean hasPartiallyReceived = false;

        for (PurchaseOrderItemVo item : items) {
            // ✅ 修正：通过产品ID获取收货数量，支持采购订单明细与入库明细的正确关联
            BigDecimal newReceivedQty = finishQuantity.get(item.getProductId());
            if (newReceivedQty != null) {
                // 校验收货数量
                if (newReceivedQty.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("收货数量必须大于0：产品【" + item.getProductCode() + "】");
                }

                // 更新已收货数量
                BigDecimal currentReceived = item.getFinishQuantity() != null ? item.getFinishQuantity() : BigDecimal.ZERO;
                BigDecimal newTotalReceived = currentReceived.add(newReceivedQty);

                // 校验收货数量不能超过订单数量
                if (newTotalReceived.compareTo(item.getQuantity()) > 0) {
                    throw new ServiceException("收货数量不能超过订单数量：产品【" + item.getProductCode() +
                        "】订单数量：" + item.getQuantity() + "，已收货：" + currentReceived + "，本次收货：" + newReceivedQty);
                }

                PurchaseOrderItem updateBo = new PurchaseOrderItem();
                updateBo.setItemId(item.getItemId());
                updateBo.setFinishQuantity(newTotalReceived);
                // 复制其他必要字段
                updateBo.setOrderId(item.getOrderId());
                updateBo.setProductId(item.getProductId());
                updateBo.setQuantity(item.getQuantity());
                updateBo.setPrice(item.getPrice());

                boolean updateResult = itemMapper.updateById(updateBo) > 0;
                if (!updateResult) {
                    throw new ServiceException("更新收货数量失败：产品【" + item.getProductCode() + "】");
                }
            }

            // 检查收货状态 - 需要重新查询更新后的数据
            PurchaseOrderItemVo updatedItem = itemMapper.selectVoById(item.getItemId());
            BigDecimal totalReceived = updatedItem.getFinishQuantity() != null ? updatedItem.getFinishQuantity() : BigDecimal.ZERO;
            if (totalReceived.compareTo(updatedItem.getQuantity()) < 0) {
                allFullyReceived = false;
            }
            if (totalReceived.compareTo(BigDecimal.ZERO) > 0) {
                hasPartiallyReceived = true;
            }
        }

        // 更新订单状态
        PurchaseOrderStatus newStatus;
        if (allFullyReceived) {
            newStatus = PurchaseOrderStatus.FULLY_RECEIVED;
        } else if (hasPartiallyReceived) {
            newStatus = PurchaseOrderStatus.PARTIALLY_RECEIVED;
        } else {
            newStatus = order.getOrderStatus(); // 保持原状态
        }

        if (!newStatus.equals(order.getOrderStatus())) {
            order.setOrderStatus(newStatus);
            int updateResult = baseMapper.updateById(order);
            if (updateResult <= 0) {
                throw new ServiceException("更新订单状态失败：订单【" + order.getOrderCode() + "】");
            }
            log.info("采购订单【{}】状态更新为：{}", order.getOrderCode(), newStatus);
        }

        return true;
    }

    /**
     * 判断是否需要审批
     * TODO: 重要功能 - 审批规则配置，支持多级审批
     *
     * @param order 采购订单
     * @return 是否需要审批
     */
    private boolean needApproval(PurchaseOrder order) {
        try {
            // 根据订单金额判断是否需要审批
            if (needApprovalByAmount(order)) {
                log.info("采购订单【{}】因金额超过阈值需要审批", order.getOrderCode());
                return true;
            }

            // 根据供应商类型判断是否需要审批
            if (needApprovalBySupplier(order)) {
                log.info("采购订单【{}】因供应商类型需要审批", order.getOrderCode());
                return true;
            }

            // 根据产品类型判断是否需要审批
            if (needApprovalByProduct(order)) {
                log.info("采购订单【{}】因产品类型需要审批", order.getOrderCode());
                return true;
            }

            // 根据用户权限判断是否需要审批
            if (needApprovalByUserPermission(order)) {
                log.info("采购订单【{}】因用户权限不足需要审批", order.getOrderCode());
                return true;
            }

            log.info("采购订单【{}】无需审批，可直接确认", order.getOrderCode());
            return false;

        } catch (Exception e) {
            log.error("判断是否需要审批失败：{}", e.getMessage(), e);
            // 出现异常时，为了安全起见，默认需要审批
            return true;
        }
    }

    /**
     * 根据订单金额判断是否需要审批
     */
    private boolean needApprovalByAmount(PurchaseOrder order) {
        // TODO: PurchaseOrder实体中没有amount字段，需要重新设计金额审批逻辑
        // 暂时通过明细汇总计算金额，或者返回默认值
        log.warn("订单金额审批判断需要重新设计 - 订单ID: {}", order.getOrderId());

        // 暂时返回true，表示需要审批（保守策略）
        return true;

        // 原逻辑（待实体完善后启用）：
        // if (order.getAmount() == null) {
        //     return false;
        // }
        // BigDecimal approvalThreshold = getApprovalAmountThreshold();
        // return order.getAmount().compareTo(approvalThreshold) > 0;
    }

    /**
     * 根据供应商类型判断是否需要审批
     */
    private boolean needApprovalBySupplier(PurchaseOrder order) {
        if (order.getSupplierId() == null) {
            return false;
        }

        // 检查供应商是否为新供应商或高风险供应商
        try {
            CompanyVo supplier = companyService.queryById(order.getSupplierId());
            if (supplier != null) {
                // 根据供应商类型判断是否需要审批
                // 新供应商或高风险供应商需要审批
                String supplierType = supplier.getCompanyType();
                boolean needApproval = "NEW".equals(supplierType) ||
                    "HIGH_RISK".equals(supplierType) ||
                    "RESTRICTED".equals(supplierType);

                log.debug("供应商审批检查 - 供应商: {}, 类型: {}, 需要审批: {}",
                    supplier.getCompanyName(), supplierType, needApproval);
                return needApproval;
            }

            log.warn("供应商信息不存在，默认需要审批 - 供应商ID: {}", order.getSupplierId());
            return true; // 供应商不存在默认需要审批
        } catch (Exception e) {
            log.warn("查询供应商信息失败，默认需要审批 - 供应商ID: {}, 错误: {}",
                order.getSupplierId(), e.getMessage());
            return true;
        }
    }

    /**
     * 根据产品类型判断是否需要审批
     */
    private boolean needApprovalByProduct(PurchaseOrder order) {
        try {
            // 获取订单明细，检查是否包含特殊产品类型
            List<PurchaseOrderItemVo> items = itemMapper.selectListByOrderId(order.getOrderId());

            for (PurchaseOrderItemVo item : items) {
                // 查询产品信息，判断产品类型
                try {
                    ProductVo product = productService.queryById(item.getProductId());
                    if (product != null) {
                        // 根据产品类型判断是否需要审批
                        String productType = product.getProductType();
                        boolean needApproval = "CONTROLLED".equals(productType) ||
                            "HAZARDOUS".equals(productType) ||
                            "HIGH_VALUE".equals(productType) ||
                            "RESTRICTED".equals(productType);

                        if (needApproval) {
                            log.debug("产品需要审批 - 产品: {}, 类型: {}",
                                product.getProductName(), productType);
                            return true;
                        }
                    }
                } catch (Exception e) {
                    log.warn("查询产品信息失败 - 产品ID: {}, 错误: {}",
                        item.getProductId(), e.getMessage());
                    // 查询失败的产品默认需要审批
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("查询产品信息失败，默认需要审批 - 订单ID: {}", order.getOrderId());
            return true;
        }
    }

    /**
     * 根据用户权限判断是否需要审批
     */
    private boolean needApprovalByUserPermission(PurchaseOrder order) {
        try {
            Long currentUserId = LoginHelper.getUserId();

            // 检查当前用户是否有直接确认采购订单的权限
            BigDecimal userApprovalLimit = getUserApprovalLimit(currentUserId);

            // TODO: PurchaseOrder实体中没有amount字段，需要重新设计权限判断逻辑
            // 暂时通过明细汇总计算金额，或者返回默认值
            log.warn("用户权限判断需要重新设计 - 订单ID: {}, 用户ID: {}", order.getOrderId(), currentUserId);

            // 原逻辑（待实体完善后启用）：
            // if (order.getAmount() != null && userApprovalLimit != null) {
            //     return order.getAmount().compareTo(userApprovalLimit) > 0;
            // }

            // 如果无法获取权限信息，默认需要审批
            return true;

        } catch (Exception e) {
            log.warn("检查用户权限失败，默认需要审批 - 错误: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 获取审批金额阈值
     */
    private BigDecimal getApprovalAmountThreshold() {
        // TODO: 从系统配置中获取审批金额阈值
        // 暂时使用硬编码值
        return new BigDecimal("10000.00");
    }

    /**
     * 获取用户的采购审批权限额度
     */
    private BigDecimal getUserApprovalLimit(Long userId) {
        // TODO: 从用户权限配置中获取审批额度
        // 暂时返回默认值
        return new BigDecimal("5000.00");
    }

    /**
     * 提交审批流程
     * 与 warm-flow 工作流引擎集成，实现采购订单的审批流程管理
     *
     * @param order 采购订单
     * @return 是否提交成功
     */
    private boolean submitForApproval(PurchaseOrder order) {
        try {
            log.info("开始提交采购订单【{}】审批", order.getOrderCode());

            // TODO: [warm-flow工作流引擎集成] - 优先级: HIGH - 参考文档: docs/design/README_FLOW.md
            // 需要完善工作流引擎集成逻辑：
            // 1. 准备审批流程参数：订单金额、供应商信息、紧急程度等
            // 2. 调用 warm-flow API 启动审批流程实例
            // 3. 获取流程实例ID，但不存储在业务表中（遵循外部流程控制模式）
            // 4. 设置审批路由规则：金额阈值、部门层级、特殊供应商等
            // 5. 配置审批超时处理和自动升级机制

            Map<String, Object> approvalVariables = prepareApprovalVariables(order);
            startApprovalProcess(order, approvalVariables);

            // 更新业务状态（不存储流程引擎内部字段）
            order.setOrderStatus(PurchaseOrderStatus.PENDING_APPROVAL);
            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // 发送审批通知
                sendApprovalNotification(order);
                log.info("采购订单【{}】已成功提交审批", order.getOrderCode());
            }

            return result;
        } catch (Exception e) {
            log.error("提交审批失败：{}", e.getMessage(), e);
            throw new ServiceException("提交审批失败：" + e.getMessage());
        }
    }

    /**
     * 准备审批流程参数
     */
    private Map<String, Object> prepareApprovalVariables(PurchaseOrder order) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("orderId", order.getOrderId());
        variables.put("orderCode", order.getOrderCode());

        // TODO: PurchaseOrder实体中没有amount字段，需要重新设计审批变量
        // variables.put("amount", order.getAmount());
        variables.put("amount", BigDecimal.ZERO); // 暂时设置为0，待实体完善后修正

        variables.put("supplierId", order.getSupplierId());
        variables.put("supplierName", order.getSupplierName());
        variables.put("applicantId", order.getApplicantId());
        variables.put("applicantName", order.getApplicantName());
        variables.put("orderDate", order.getOrderDate());

        // TODO: PurchaseOrder实体中没有expectedDate字段，需要重新设计审批变量
        // variables.put("expectedDate", order.getExpectedDate());
        variables.put("expectedDate", null); // 暂时设置为null，待实体完善后修正

        variables.put("remark", order.getRemark());

        // 添加审批级别判断
        String approvalLevel = determineApprovalLevel(order);
        variables.put("approvalLevel", approvalLevel);

        log.debug("准备审批流程参数完成 - 订单: {}, 审批级别: {}", order.getOrderCode(), approvalLevel);
        return variables;
    }

    /**
     * 启动审批流程
     */
    private void startApprovalProcess(PurchaseOrder order, Map<String, Object> variables) {
        try {
            // TODO: 集成 warm-flow 流程引擎
            // 示例代码：
            // String processDefinitionKey = "purchase_order_approval";
            // String businessKey = order.getOrderCode();
            // warmFlowService.startProcess(processDefinitionKey, businessKey, variables);

            log.info("启动采购订单审批流程 - 订单: {}", order.getOrderCode());

            // 注意：warm-flow 采用外部流程控制业务表状态的架构模式
            // 流程实例ID等内部字段由流程引擎管理，业务表不存储
        } catch (Exception e) {
            log.error("启动审批流程失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            throw new ServiceException("启动审批流程失败：" + e.getMessage());
        }
    }

    /**
     * 确定审批级别
     */
    private String determineApprovalLevel(PurchaseOrder order) {
        // TODO: PurchaseOrder实体中没有amount字段，需要重新设计审批级别判断逻辑
        // 暂时返回默认级别，或者通过明细汇总计算金额
        log.warn("审批级别判断需要重新设计 - 订单ID: {}", order.getOrderId());

        // 暂时返回LEVEL_1（保守策略）
        return "LEVEL_1";

        // 原逻辑（待实体完善后启用）：
        // if (order.getAmount() == null) {
        //     return "LEVEL_1";
        // }
        // BigDecimal amount = order.getAmount();
        // if (amount.compareTo(new BigDecimal("100000")) > 0) {
        //     return "LEVEL_3"; // 高级审批
        // } else if (amount.compareTo(new BigDecimal("50000")) > 0) {
        //     return "LEVEL_2"; // 中级审批
        // } else {
        //     return "LEVEL_1"; // 初级审批
        // }
    }

    /**
     * 发送审批通知
     */
    private void sendApprovalNotification(PurchaseOrder order) {
        try {
            // TODO: 实现审批通知发送
            // 确定审批人
            // 发送邮件通知
            // 发送系统内通知
            // 发送微信/钉钉通知（如果集成）

            log.info("已发送审批通知 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("发送审批通知失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 审批通过
     * TODO: 重要功能 - 审批结果处理
     *
     * @param orderId         订单ID
     * @param approvalComment 审批意见
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean approveOrder(Long orderId, String approvalComment) {
        try {
            PurchaseOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 验证订单状态
            if (PurchaseOrderStatus.PENDING_APPROVAL != order.getOrderStatus()) {
                throw new ServiceException("订单状态不正确，当前状态：" + order.getOrderStatus() + "，无法审批");
            }

            // 记录审批信息（只记录业务相关字段）
            Long approverId = LoginHelper.getUserId();
            String approverName = LoginHelper.getLoginUser().getNickname();

            // 更新订单状态和审批信息
            order.setOrderStatus(PurchaseOrderStatus.CONFIRMED);
            order.setApproverId(approverId);
            order.setApproverName(approverName);
            // 移除流程时间字段操作：
            // - 删除 order.setApproveTime(approveTime)
            // 审批时间应由 warm-flow 流程引擎管理

            // 添加审批意见到备注
            if (StringUtils.isNotBlank(approvalComment)) {
                String newRemark = (order.getRemark() != null ? order.getRemark() : "") +
                    String.format(" [审批通过 - %s: %s]", approverName, approvalComment);
                order.setRemark(newRemark);
            }

            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // 执行审批通过后的业务逻辑
                executeApprovalPassedLogic(order);

                log.info("采购订单【{}】审批通过 - 审批人: {}",
                    order.getOrderCode(), approverName);
            }

            return result;
        } catch (Exception e) {
            log.error("审批订单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("审批订单失败：" + e.getMessage());
        }
    }

    /**
     * 审批拒绝
     * TODO: 重要功能 - 审批拒绝处理
     *
     * @param orderId      订单ID
     * @param rejectReason 拒绝原因
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectOrder(Long orderId, String rejectReason) {
        try {
            PurchaseOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 验证订单状态
            if (PurchaseOrderStatus.PENDING_APPROVAL != order.getOrderStatus()) {
                throw new ServiceException("订单状态不正确，当前状态：" + order.getOrderStatus() + "，无法拒绝");
            }

            // 验证拒绝原因
            if (StringUtils.isBlank(rejectReason)) {
                throw new ServiceException("拒绝原因不能为空");
            }

            // 记录审批信息（只记录业务相关字段）
            Long approverId = LoginHelper.getUserId();
            String approverName = LoginHelper.getLoginUser().getNickname();

            // 更新订单状态为草稿，允许重新修改
            order.setOrderStatus(PurchaseOrderStatus.DRAFT);
            order.setApproverId(approverId);
            order.setApproverName(approverName);
            order.setApproveTime(LocalDateTime.now());
            // 后续接入流程引擎审批时间应由 warm-flow 流程引擎管理

            // 添加拒绝原因到备注
            String newRemark = (order.getRemark() != null ? order.getRemark() : "") +
                String.format(" [审批拒绝 - %s: %s]", approverName, rejectReason);
            order.setRemark(newRemark);

            // 移除流程实例字段操作：
            // - 删除 order.setProcessInstanceId(null)
            // 流程实例ID由 warm-flow 流程引擎管理，业务表不存储此字段

            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // 执行审批拒绝后的业务逻辑
                executeApprovalRejectedLogic(order, rejectReason);

                log.info("采购订单【{}】审批拒绝 - 审批人: {}, 拒绝原因: {}",
                    order.getOrderCode(), approverName, rejectReason);
            }

            return result;
        } catch (Exception e) {
            log.error("拒绝订单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("拒绝订单失败：" + e.getMessage());
        }
    }

    /**
     * 执行审批通过后的业务逻辑
     */
    private void executeApprovalPassedLogic(PurchaseOrder order) {
        try {
            // 发送审批通过通知
            sendApprovalPassedNotification(order);

            // 更新相关业务状态
            updateRelatedBusinessStatus(order, "APPROVED");

            // 触发后续业务流程
            triggerSubsequentProcess(order);

            log.info("审批通过后业务逻辑执行完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("执行审批通过后业务逻辑失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 执行审批拒绝后的业务逻辑
     */
    private void executeApprovalRejectedLogic(PurchaseOrder order, String rejectReason) {
        try {
            // 发送审批拒绝通知
            sendApprovalRejectedNotification(order, rejectReason);

            // 更新相关业务状态
            updateRelatedBusinessStatus(order, "REJECTED");

            // 清理相关资源
            cleanupRelatedResources(order);

            log.info("审批拒绝后业务逻辑执行完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.warn("执行审批拒绝后业务逻辑失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 发送审批通过通知
     */
    private void sendApprovalPassedNotification(PurchaseOrder order) {
        // TODO: 实现审批通过通知
        // 通知申请人审批通过
        // 通知相关业务人员
        // 记录通知日志
        log.info("发送审批通过通知 - 订单: {}", order.getOrderCode());
    }

    /**
     * 发送审批拒绝通知
     */
    private void sendApprovalRejectedNotification(PurchaseOrder order, String rejectReason) {
        // TODO: 实现审批拒绝通知
        // 通知申请人审批拒绝及原因
        // 提供修改建议
        // 记录通知日志
        log.info("发送审批拒绝通知 - 订单: {}, 拒绝原因: {}", order.getOrderCode(), rejectReason);
    }

    /**
     * 更新相关业务状态
     */
    private void updateRelatedBusinessStatus(PurchaseOrder order, String approvalResult) {
        // TODO: 更新相关业务状态
        // 更新供应商合作状态
        // 更新产品采购状态
        // 更新预算使用状态
        log.debug("更新相关业务状态 - 订单: {}, 审批结果: {}", order.getOrderCode(), approvalResult);
    }

    /**
     * 触发后续业务流程
     */
    private void triggerSubsequentProcess(PurchaseOrder order) {
        try {
            // 创建采购入库单
            createPurchaseInbound(order.getOrderId());

            // 更新库存预留
            updateInventoryReservation(order);

            // 通知供应商发货
            notifySupplierForDelivery(order);

            log.info("后续业务流程触发完成 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.error("触发后续业务流程失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新库存预留
     */
    private void updateInventoryReservation(PurchaseOrder order) {
        try {
            // TODO: 实现库存预留更新逻辑
            // 预留采购数量
            // 更新预计到货时间
            log.debug("更新库存预留 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.error("更新库存预留失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
        }
    }

    /**
     * 通知供应商发货
     */
    private void notifySupplierForDelivery(PurchaseOrder order) {
        try {
            // TODO: 实现供应商发货通知
            // 发送采购订单给供应商
            // 通知预计到货时间
            // 提供收货地址和联系人
            log.debug("通知供应商发货 - 订单: {}", order.getOrderCode());
        } catch (Exception e) {
            log.error("通知供应商发货失败 - 订单: {}, 错误: {}", order.getOrderCode(), e.getMessage(), e);
        }
    }

    /**
     * 清理相关资源
     */
    private void cleanupRelatedResources(PurchaseOrder order) {
        // TODO: 清理相关资源
        // 释放预留库存
        // 释放预算占用
        // 清理临时数据
        log.debug("清理相关资源 - 订单: {}", order.getOrderCode());
    }

    /**
     * 自动填充责任人信息
     *
     * @param bo 采购订单业务对象
     */
    private void fillResponsiblePersons(PurchaseOrderBo bo) {
        try {
            // 获取当前登录用户信息
            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                // 如果申请人为空，设置为当前用户
                if (bo.getApplicantId() == null) {
                    bo.setApplicantId(loginUser.getUserId());
                    bo.setApplicantName(loginUser.getNickname());
                }

                // 如果采购员为空，设置为当前用户（如果当前用户是采购员）
                if (bo.getHandlerId() == null) {
                    // TODO: 根据用户角色判断是否为采购员
                    // 这里可以根据用户的角色或部门来自动分配采购员
                    bo.setHandlerId(loginUser.getUserId());
                    bo.setHandlerName(loginUser.getNickname());
                }

                // 设置提交时间
                if (bo.getOrderDate() == null) {
                    bo.setOrderDate(LocalDate.now());
                }
            }

            log.debug("自动填充采购订单责任人信息完成 - 申请人: {}, 采购员: {}",
                bo.getApplicantName(), bo.getHandlerName());
        } catch (Exception e) {
            log.warn("自动填充责任人信息失败: {}", e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从明细汇总到主表
     *
     * @param orderId 订单ID
     * @return 是否汇总成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean summarizeFromItems(Long orderId) {
        try {
            PurchaseOrder order = baseMapper.selectById(orderId);
            if (order == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 获取订单明细列表
            List<PurchaseOrderItemVo> items = itemMapper.selectListByOrderId(orderId);

            // TODO: PurchaseOrder实体中没有amount相关字段，需要重新设计明细汇总逻辑
            // 暂时注释掉金额汇总逻辑，待实体完善后启用
            log.warn("明细汇总需要重新设计 - 订单ID: {}, 明细数量: {}", orderId, items.size());

            // 原逻辑（待实体完善后启用）：
            // if (items.isEmpty()) {
            //     // 如果没有明细，清零主表金额
            //     order.setAmountExclusiveTax(BigDecimal.ZERO);
            //     order.setTaxAmount(BigDecimal.ZERO);
            //     order.setAmount(BigDecimal.ZERO);
            // } else {
            //     // 汇总计算各项金额
            //     BigDecimal amountExclusiveTax = items.stream()
            //         .map(item -> item.getAmountExclusiveTax() != null ? item.getAmountExclusiveTax() : BigDecimal.ZERO)
            //         .reduce(BigDecimal.ZERO, BigDecimal::add);
            //
            //     BigDecimal totalTaxAmount = items.stream()
            //         .map(item -> item.getTaxAmount() != null ? item.getTaxAmount() : BigDecimal.ZERO)
            //         .reduce(BigDecimal.ZERO, BigDecimal::add);
            //
            //     BigDecimal amount = items.stream()
            //         .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
            //         .reduce(BigDecimal.ZERO, BigDecimal::add);
            //
            //     // 更新主表金额
            //     order.setAmountExclusiveTax(amountExclusiveTax);
            //     order.setTaxAmount(totalTaxAmount);
            //     order.setAmount(amount);
            // }

            boolean result = baseMapper.updateById(order) > 0;

            if (result) {
                // TODO: PurchaseOrder实体中没有amount相关字段，暂时简化日志输出
                log.info("采购订单明细汇总成功 - 订单: {}, 明细数量: {}",
                    order.getOrderCode(), items.size());

                // 原日志（待实体完善后启用）：
                // log.info("采购订单明细汇总成功 - 订单: {}, 金额(不含税): {}, 税额: {}, 总金额: {}",
                //     order.getOrderCode(), order.getAmountExclusiveTax(),
                //     order.getTaxAmount(), order.getAmount());
            }

            return result;
        } catch (Exception e) {
            log.error("采购订单明细汇总失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("采购订单明细汇总失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否有关联的发票
     *
     * @param orderId 订单ID
     */
    private void checkRelatedInvoices(Long orderId) {
        try {
            log.debug("检查采购订单关联发票 - 订单ID: {}", orderId);

            // TODO: 后续集成发票模块时完善发票关联检查
            // 当前简化处理：只记录日志，不阻止删除操作
            // 实际项目中的实现示例：
            // if (finApInvoiceItemService.existsBySourceOrderId(orderId)) {
            //     throw new ServiceException("采购订单已有关联发票，不允许删除");
            // }

            log.debug("采购订单关联发票检查完成 - 订单ID: {}", orderId);
        } catch (Exception e) {
            log.warn("检查关联发票时发生异常 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
            // 简化处理：不因为发票检查失败而阻止删除操作
        }
    }

    /**
     * 检查用户角色
     *
     * @param userId 用户ID
     * @return 是否为采购员
     */
    private boolean checkUserRole(Long userId) {
        try {
            // TODO: 实现用户角色检查逻辑
            // 查询用户角色信息
            // 判断是否包含采购员角色
            // 检查用户所属部门是否为采购部门

            log.debug("检查用户角色 - 用户ID: {}", userId);

            // 暂时返回true，后续集成用户权限模块时完善
            // 实际项目中可能的实现：
            // List<String> userRoles = userService.getUserRoles(userId);
            // return userRoles.contains("PURCHASER") || userRoles.contains("PURCHASE_MANAGER");

            return true;
        } catch (Exception e) {
            log.warn("检查用户角色失败 - 用户ID: {}, 错误: {}", userId, e.getMessage());
            // 检查失败时返回false，限制权限
            return false;
        }
    }

    /**
     * 采购订单财务对账
     *
     * @param purchaseOrderId 采购订单ID
     * @return 对账结果
     */
    @Override
    public Map<String, Object> reconcilePurchaseOrder(Long purchaseOrderId) {
        try {
            log.info("开始采购订单财务对账 - 订单ID: {}", purchaseOrderId);

            Map<String, Object> result = new java.util.HashMap<>();

            // 采购订单信息
            PurchaseOrderVo purchaseOrder = queryById(purchaseOrderId);
            if (purchaseOrder == null) {
                throw new ServiceException("采购订单不存在");
            }
            result.put("purchaseOrder", purchaseOrder);

            // 关联的入库单信息
            // TODO: 查询关联的入库单信息
            // List<PurchaseInboundVo> inbounds = purchaseInboundService.queryByOrderId(purchaseOrderId);
            // result.put("inbounds", inbounds);
            result.put("inbounds", new ArrayList<>());

            // 生成的应付单信息
            // TODO: 查询生成的应付单信息
            // List<FinApInvoiceVo> invoices = finApInvoiceService.queryByOrderId(purchaseOrderId);
            // result.put("invoices", invoices);
            result.put("invoices", new ArrayList<>());

            // 相关的付款单信息
            // TODO: 查询相关的付款单信息
            // List<FinApPaymentOrderVo> payments = finApPaymentOrderService.queryByOrderId(purchaseOrderId);
            // result.put("payments", payments);
            result.put("payments", new ArrayList<>());

            // 核销记录信息
            // TODO: 查询核销记录信息
            // List<FinApPaymentInvoiceLinkVo> links = finApPaymentInvoiceLinkService.queryByOrderId(purchaseOrderId);
            // result.put("links", links);
            result.put("links", new ArrayList<>());

            // 账户流水信息
            // TODO: 查询账户流水信息
            // List<FinAccountLedgerVo> ledgers = finAccountLedgerService.queryByOrderId(purchaseOrderId);
            // result.put("ledgers", ledgers);
            result.put("ledgers", new ArrayList<>());

            // 对账结果分析
            Map<String, Object> analysis = new java.util.HashMap<>();
            // TODO: PurchaseOrder实体中没有amount字段，需要重新设计对账分析逻辑
            // 暂时设置为0，待实体完善后通过明细汇总计算
            analysis.put("orderAmount", BigDecimal.ZERO); // 原: purchaseOrder.getAmount()
            analysis.put("invoiceAmount", BigDecimal.ZERO);  // TODO: 计算应付金额
            analysis.put("paymentAmount", BigDecimal.ZERO);  // TODO: 计算付款金额
            analysis.put("balanceAmount", BigDecimal.ZERO);  // TODO: 计算余额
            analysis.put("reconcileStatus", "PENDING");      // TODO: 计算对账状态
            result.put("analysis", analysis);

            result.put("success", true);
            result.put("message", "采购订单财务对账查询成功");

            log.info("采购订单财务对账完成 - 订单ID: {}", purchaseOrderId);
            return result;
        } catch (Exception e) {
            log.error("采购订单财务对账失败 - 订单ID: {}, 错误: {}", purchaseOrderId, e.getMessage(), e);
            throw new ServiceException("采购订单财务对账失败：" + e.getMessage());
        }
    }

    /**
     * 创建采购入库单
     *
     * @param orderId 采购订单ID
     * @return 是否创建成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createPurchaseInbound(Long orderId) {
        try {
            PurchaseOrderVo order = createInboundValid(orderId);
            // 检查是否已有入库单
            /*if (purchaseInboundService.existsByOrderId(orderId)) {
                throw new ServiceException("该订单已有入库单，不能重复创建");
            }*/
            // 调用入库单服务创建入库单
            Boolean createResult = purchaseInboundService.createFromPurchaseOrder(order);
            if (!createResult) {
                throw new ServiceException("创建入库单失败");
            }
            log.info("创建入库单成功 - 订单: {}", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("创建入库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("创建入库单失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建采购入库单
     *
     * @param orderIds 采购订单ID列表
     * @return 创建成功的数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchCreatePurchaseInbound(List<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                throw new ServiceException("订单ID列表不能为空");
            }
            int successCount = 0;
            for (Long orderId : orderIds) {
                try {
                    Boolean result = createPurchaseInbound(orderId);
                    if (result != null && result) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.warn("批量创建入库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
                    // 继续处理其他订单，不中断整个批量操作
                }
            }
            log.info("批量创建入库单完成 - 总数: {}, 成功: {}", orderIds.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量创建入库单失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量创建入库单失败：" + e.getMessage());
        }
    }

    /**
     * 创建仓库入库单
     *
     * @param orderId 采购订单ID
     * @return
     */
    @Override
    public Boolean createInbound(Long orderId) {
        try {
            PurchaseOrderVo order = createInboundValid(orderId);
            // 检查是否已有入库单
            /*if (inboundService.existsByOrderId(orderId)) {
                throw new ServiceException("该订单已有入库单，不能重复创建");
            }*/
            // 调用入库单服务创建入库单
            Boolean createResult = inboundService.createFromPurchaseOrder(order);
            if (!createResult) {
                throw new ServiceException("创建入库单失败");
            }
            log.info("创建入库单成功 - 订单: {}", order.getOrderCode());
            return true;
        } catch (Exception e) {
            log.error("创建入库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("创建入库单失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建仓库入库单
     *
     * @param orderIds 采购订单ID列表
     * @return 创建成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer batchCreateInbound(List<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                throw new ServiceException("订单ID列表不能为空");
            }

            int successCount = 0;
            for (Long orderId : orderIds) {
                try {
                    Boolean result = createPurchaseInbound(orderId);
                    if (result != null && result) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.warn("批量创建入库单失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage());
                    // 继续处理其他订单，不中断整个批量操作
                }
            }
            log.info("批量创建入库单完成 - 总数: {}, 成功: {}", orderIds.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量创建入库单失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量创建入库单失败：" + e.getMessage());
        }
    }


    /**
     * 创建入库前置验证
     *
     * @param orderId 订单 ID
     * @return 订单信息
     */
    private PurchaseOrderVo createInboundValid(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("订单ID不能为空");
        }
        // 检查订单是否存在
        PurchaseOrderVo order = baseMapper.selectVoById(orderId);
        if (order == null) {
            throw new ServiceException("采购订单不存在");
        }
        // 检查订单状态
        if (order.getOrderStatus() != PurchaseOrderStatus.CONFIRMED) {
            throw new ServiceException("只有已确认的订单才能创建入库单");
        }
        // 检查订单明细
        List<PurchaseOrderItemVo> items = itemMapper.selectListByOrderId(orderId);
        if (items == null || items.isEmpty()) {
            throw new ServiceException("采购订单没有明细，不能创建入库单");
        } else {
            for (PurchaseOrderItemVo item : items) {
                if (item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("采购订单明细数量不能小于等于0");
                }
            }
            order.setItems(items);
        }
        return order;
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(PurchaseOrderBo bo) {
        // 填充供应商信息
        if (bo.getSupplierId() != null && StringUtils.isEmpty(bo.getSupplierName())) {
            CompanyVo supplier = companyService.queryById(bo.getSupplierId());
            if (supplier != null) {
                bo.setSupplierName(supplier.getCompanyName());
            }
        }
        //填充责任人信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser != null) {
            // 如果是新增，设置申请人
            if (bo.getOrderId() == null) {
                bo.setApplicantId(loginUser.getUserId());
                bo.setApplicantName(loginUser.getNickname());
            }
            // 设置经办人（每次更新都更新）
            bo.setHandlerId(loginUser.getUserId());
            bo.setHandlerName(loginUser.getNickname());
        }
    }

    /**
     * 校验状态流转合法性
     */
    private boolean isValidStatusTransition(PurchaseOrderStatus fromStatus, PurchaseOrderStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return true;
        }

        // 定义合法的状态流转
        switch (fromStatus) {
            case DRAFT:
                return toStatus == PurchaseOrderStatus.CONFIRMED ||
                    toStatus == PurchaseOrderStatus.DRAFT;
            case CONFIRMED:
                return toStatus == PurchaseOrderStatus.PARTIALLY_RECEIVED ||
                    toStatus == PurchaseOrderStatus.FULLY_RECEIVED ||
                    toStatus == PurchaseOrderStatus.CANCELLED;
            case PARTIALLY_RECEIVED:
                return toStatus == PurchaseOrderStatus.FULLY_RECEIVED ||
                    toStatus == PurchaseOrderStatus.CANCELLED;
            case FULLY_RECEIVED:
                return toStatus == PurchaseOrderStatus.CLOSED;
            case CANCELLED:
            case CLOSED:
                return toStatus == fromStatus; // 终态，不能再变更
            default:
                return false;
        }
    }

    // ==================== BOM库存分析相关方法 ====================

    /**
     * 基于BOM清单生成采购建议
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 需求数量
     * @param urgencyLevel     紧急程度
     * @return 采购建议清单
     */
    public List<PurchaseRequirementVo> generatePurchaseSuggestionsByBom(Long bomId, BigDecimal requiredQuantity, String urgencyLevel) {
        log.info("基于BOM生成采购建议 - BOM ID: {}, 需求数量: {}, 紧急程度: {}", bomId, requiredQuantity, urgencyLevel);

        try {
            // 进行BOM库存分析
            BomInventoryAnalysisVo analysis = bomInventoryAnalysisService.analyzeBomInventory(bomId, requiredQuantity);

            // 记录分析日志
            log.info("BOM库存分析完成 - 状态: {}, 缺料数: {}, 预警数: {}",
                analysis.getInventoryStatus(), analysis.getShortageCount(), analysis.getWarningCount());

            // 如果库存充足，无需采购
            if ("SUFFICIENT".equals(analysis.getInventoryStatus()) && analysis.getShortageCount() == 0) {
                log.info("库存充足，无需采购 - BOM ID: {}", bomId);
                return new ArrayList<>();
            }

            // 生成采购建议
            List<PurchaseRequirementVo> suggestions = bomInventoryAnalysisService.generatePurchaseSuggestions(
                bomId, requiredQuantity, urgencyLevel);

            // 记录业务日志
            if (!suggestions.isEmpty()) {
                PurchaseRequirementVo mainSuggestion = suggestions.get(0);
                log.info("采购建议生成成功 - BOM ID: {}, 采购项目数: {}, 总金额: {}",
                    bomId, mainSuggestion.getTotalPurchaseItems(), mainSuggestion.getTotalPurchaseAmount());
            }

            return suggestions;

        } catch (Exception e) {
            log.error("基于BOM生成采购建议失败 - BOM ID: {}, 错误: {}", bomId, e.getMessage(), e);
            throw new ServiceException("生成采购建议失败：" + e.getMessage());
        }
    }

    /**
     * 一键生成采购订单（基于BOM分析）
     *
     * @param bomId            BOM清单ID
     * @param requiredQuantity 需求数量
     * @param urgencyLevel     紧急程度
     * @param supplierId       指定供应商ID（可选）
     * @return 生成的采购订单ID列表
     */
    public List<Long> generatePurchaseOrdersByBom(Long bomId, BigDecimal requiredQuantity, String urgencyLevel, Long supplierId) {
        log.info("一键生成采购订单 - BOM ID: {}, 需求数量: {}, 紧急程度: {}, 供应商ID: {}",
            bomId, requiredQuantity, urgencyLevel, supplierId);

        try {
            List<Long> orderIds = new ArrayList<>();

            // 生成采购建议
            List<PurchaseRequirementVo> suggestions = generatePurchaseSuggestionsByBom(bomId, requiredQuantity, urgencyLevel);

            if (suggestions.isEmpty()) {
                log.info("无需采购，返回空列表 - BOM ID: {}", bomId);
                return orderIds;
            }

            // 按供应商分组采购建议
            Map<Long, List<PurchaseRequirementVo.PurchaseSuggestionVo>> supplierGroups = new HashMap<>();

            for (PurchaseRequirementVo requirement : suggestions) {
                for (PurchaseRequirementVo.PurchaseSuggestionVo suggestion : requirement.getPurchaseSuggestions()) {
                    Long targetSupplierId = supplierId != null ? supplierId : suggestion.getSuggestedSupplierId();
                    if (targetSupplierId == null) {
                        targetSupplierId = getDefaultSupplierId(); // 获取默认供应商
                    }

                    supplierGroups.computeIfAbsent(targetSupplierId, k -> new ArrayList<>()).add(suggestion);
                }
            }

            // 为每个供应商创建采购订单
            for (Map.Entry<Long, List<PurchaseRequirementVo.PurchaseSuggestionVo>> entry : supplierGroups.entrySet()) {
                Long targetSupplierId = entry.getKey();
                List<PurchaseRequirementVo.PurchaseSuggestionVo> supplierSuggestions = entry.getValue();

                Long orderId = createPurchaseOrderFromSuggestions(targetSupplierId, supplierSuggestions, urgencyLevel);
                if (orderId != null) {
                    orderIds.add(orderId);
                }
            }

            log.info("一键生成采购订单完成 - BOM ID: {}, 生成订单数: {}", bomId, orderIds.size());
            return orderIds;

        } catch (Exception e) {
            log.error("一键生成采购订单失败 - BOM ID: {}, 错误: {}", bomId, e.getMessage(), e);
            throw new ServiceException("一键生成采购订单失败：" + e.getMessage());
        }
    }

    /**
     * 检查采购订单的BOM库存影响
     *
     * @param orderId 采购订单ID
     * @return BOM库存影响分析
     */
    public Map<String, Object> analyzePurchaseOrderBomImpact(Long orderId) {
        log.info("分析采购订单的BOM库存影响 - 订单ID: {}", orderId);

        try {
            Map<String, Object> impact = new HashMap<>();

            // 获取采购订单信息
            PurchaseOrderVo order = queryById(orderId);
            if (order == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 分析订单中每个产品对BOM的影响
            List<Map<String, Object>> productImpacts = new ArrayList<>();

            /*for (PurchaseOrderItemVo item : order.getItems()) { //TODO 需完善
                Map<String, Object> productImpact = new HashMap<>();
                productImpact.put("productId", item.getProductId());
                productImpact.put("productCode", item.getProductCode());
                productImpact.put("productName", item.getProductName());
                productImpact.put("purchaseQuantity", item.getQuantity());

                // 查询该产品作为原材料影响的BOM
                List<Long> affectedBoms = findBomsUsingMaterial(item.getProductId());
                productImpact.put("affectedBomCount", affectedBoms.size());
                productImpact.put("affectedBoms", affectedBoms);

                // 计算库存改善情况
                BigDecimal currentStock = bomInventoryAnalysisService.queryMaterialInventoryBalance(item.getProductId(), null);
                BigDecimal afterPurchaseStock = currentStock.add(item.getQuantity());
                productImpact.put("currentStock", currentStock);
                productImpact.put("afterPurchaseStock", afterPurchaseStock);

                productImpacts.add(productImpact);
            }*/

            impact.put("orderId", orderId);
            impact.put("orderCode", order.getOrderCode());
            impact.put("supplierName", order.getSupplierName());
            impact.put("productImpacts", productImpacts);
            impact.put("analysisTime", LocalDateTime.now());

            log.info("采购订单BOM库存影响分析完成 - 订单ID: {}, 影响产品数: {}", orderId, productImpacts.size());
            return impact;

        } catch (Exception e) {
            log.error("分析采购订单BOM库存影响失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("分析采购订单BOM库存影响失败：" + e.getMessage());
        }
    }

    // 私有辅助方法

    /**
     * 获取默认供应商ID
     */
    private Long getDefaultSupplierId() {
        // 返回默认供应商ID，实际项目中可以从配置中获取
        return 1L;
    }

    /**
     * 根据采购建议创建采购订单
     */
    private Long createPurchaseOrderFromSuggestions(Long supplierId, List<PurchaseRequirementVo.PurchaseSuggestionVo> suggestions, String urgencyLevel) {
        try {
            // 创建采购订单主表
            PurchaseOrderBo orderBo = new PurchaseOrderBo();
            orderBo.setSupplierId(supplierId);
            orderBo.setOrderDate(LocalDate.now());
            orderBo.setOrderStatus(PurchaseOrderStatus.DRAFT);
            orderBo.setRemark("基于BOM分析自动生成 - 紧急程度: " + urgencyLevel);

            // 创建采购订单明细 TODO 需完善
            /*List<PurchaseOrderItemBo> itemBos = new ArrayList<>();
            for (PurchaseRequirementVo.PurchaseSuggestionVo suggestion : suggestions) {
                PurchaseOrderItemBo itemBo = new PurchaseOrderItemBo();
                itemBo.setProductId(suggestion.getMaterialId());
                itemBo.setQuantity(suggestion.getActualSuggestedQuantity());
                itemBo.setPrice(suggestion.getEstimatedPrice());
                itemBo.setRemark("BOM分析建议采购");
                itemBos.add(itemBo);
            }
            orderBo.setItems(itemBos);*/

            // 保存采购订单
            PurchaseOrderVo result = insertByBo(orderBo);
            if (result != null) {
                log.info("基于BOM建议创建采购订单成功 - 供应商ID: {}, 明细数: {}", supplierId, suggestions.size());
                return orderBo.getOrderId();
            } else {
                log.error("基于BOM建议创建采购订单失败 - 供应商ID: {}", supplierId);
                return null;
            }

        } catch (Exception e) {
            log.error("基于BOM建议创建采购订单异常 - 供应商ID: {}, 错误: {}", supplierId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查找使用指定原材料的BOM清单
     */
    private List<Long> findBomsUsingMaterial(Long materialId) {
        // 基于现有字段模拟查询使用该原材料的BOM
        // 实际项目中需要查询BOM明细表
        List<Long> bomIds = new ArrayList<>();

        // 模拟数据：假设每个原材料被3-5个BOM使用
        Random random = new Random(materialId);
        int bomCount = random.nextInt(3) + 3;

        for (int i = 0; i < bomCount; i++) {
            bomIds.add(materialId / 1000 + i + 1);
        }

        return bomIds;
    }
}
